#!/usr/bin/env python3
"""
测试完整的输出目录工作流程
验证 JSON 和 FBX 文件都保存到 backend/output 目录
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.utils.file import (
    save_json_to_output, get_output_file_path, ensure_output_directory, 
    list_output_files
)
from backend.agent.action_parser import ActionParser
from backend.blender.converter import GameActionToBlenderConverter
from backend.blender.fbx_exporter import FBXExporter


def test_complete_workflow():
    """测试完整的工作流程"""
    print("🎬 完整工作流程测试 - backend/output 目录")
    print("=" * 60)
    
    # 1. 确保输出目录正确
    print("\n📁 步骤 1: 验证输出目录")
    output_dir = ensure_output_directory()
    print(f"✅ 输出目录: {output_dir}")
    
    # 验证目录是在 backend 内
    expected_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "backend", "output")
    if os.path.samefile(output_dir, expected_path):
        print("✅ 输出目录位置正确 (backend/output)")
    else:
        print(f"❌ 输出目录位置错误，期望: {expected_path}")
        return False
    
    # 2. 测试动作解析和保存
    print("\n🎯 步骤 2: 测试动作解析和保存")
    parser = ActionParser()
    natural_language = "人物开心地挥手3秒，然后向前走5步，最后跳跃"
    
    try:
        actions = parser.parse_simple_command(natural_language)
        print(f"✅ 解析成功，生成了 {len(actions)} 个动作")
        
        # 保存动作到输出目录
        actions_path = save_json_to_output(actions, "complete_test_actions", add_timestamp=True)
        print(f"✅ 动作数据已保存: {actions_path}")
        
        # 验证文件确实在 backend/output 中
        if "backend/output" in actions_path:
            print("✅ 动作文件保存位置正确")
        else:
            print(f"❌ 动作文件保存位置错误: {actions_path}")
            return False
        
    except Exception as e:
        print(f"❌ 动作解析失败: {e}")
        return False
    
    # 3. 测试 Blender 转换和保存
    print("\n🎨 步骤 3: 测试 Blender 转换和保存")
    
    try:
        converter = GameActionToBlenderConverter()
        blender_project = converter.convert_actions_to_blender(actions, "CompleteTestProject")
        
        print(f"✅ Blender 转换成功")
        print(f"  项目名称: {blender_project.project_name}")
        print(f"  动画片段: {len(blender_project.animation_clips)}")
        print(f"  默认导出路径: {blender_project.export_settings.export_path}")
        
        # 验证默认导出路径是否在 backend/output 中
        if "backend/output" in blender_project.export_settings.export_path:
            print("✅ Blender 默认导出路径正确")
        else:
            print(f"❌ Blender 默认导出路径错误: {blender_project.export_settings.export_path}")
        
        # 保存 Blender 项目数据
        blender_path = save_json_to_output(
            blender_project.model_dump(), 
            "complete_test_blender_project", 
            add_timestamp=True
        )
        print(f"✅ Blender 项目数据已保存: {blender_path}")
        
    except Exception as e:
        print(f"❌ Blender 转换失败: {e}")
        return False
    
    # 4. 测试 FBX 导出路径
    print("\n📦 步骤 4: 测试 FBX 导出路径")
    
    try:
        exporter = FBXExporter()
        blender_info = exporter.get_blender_info()
        
        print(f"Blender 可用: {blender_info['available']}")
        
        if blender_info['available']:
            # 生成输出路径
            fbx_output_path = get_output_file_path("complete_test_animation.fbx", add_timestamp=True)
            print(f"✅ FBX 输出路径: {fbx_output_path}")
            
            # 验证路径在 backend/output 中
            if "backend/output" in fbx_output_path:
                print("✅ FBX 输出路径正确")
            else:
                print(f"❌ FBX 输出路径错误: {fbx_output_path}")
                return False
            
            print("正在导出 FBX 文件...")
            result = exporter.export_actions_to_fbx(
                actions=actions,
                output_path=fbx_output_path,
                project_name="CompleteTestProject",
                character_name="TestCharacter"
            )
            
            if result["success"]:
                print(f"✅ FBX 导出成功: {result['output_path']}")
                print(f"  文件大小: {result.get('file_size', 0)} 字节")
                
                # 验证文件确实在正确位置
                if os.path.exists(result['output_path']) and "backend/output" in result['output_path']:
                    print("✅ FBX 文件位置正确")
                else:
                    print(f"❌ FBX 文件位置错误或不存在: {result['output_path']}")
            else:
                print(f"⚠️ FBX 导出失败: {result.get('error')}")
                print("  (这可能是 Blender 脚本问题，不影响路径测试)")
        else:
            print(f"⚠️ 跳过 FBX 导出（Blender 不可用）: {blender_info.get('error')}")
    
    except Exception as e:
        print(f"❌ FBX 导出异常: {e}")
    
    # 5. 列出输出目录中的文件
    print("\n📋 步骤 5: 列出输出目录中的文件")
    
    try:
        # 列出所有文件
        all_files = list_output_files()
        print(f"\n📁 输出目录: {output_dir}")
        print(f"📊 总文件数: {len(all_files)}")
        
        # 按类型分组显示
        json_files = list_output_files(file_extension=".json")
        fbx_files = list_output_files(file_extension=".fbx")
        
        print(f"\n📄 JSON 文件 ({len(json_files)} 个):")
        for file_info in json_files[-5:]:  # 显示最新的5个
            print(f"  ✅ {file_info['name']} - {file_info['size']} 字节")
            print(f"     修改时间: {file_info['modified']}")
        
        if fbx_files:
            print(f"\n🎬 FBX 文件 ({len(fbx_files)} 个):")
            for file_info in fbx_files[-3:]:  # 显示最新的3个
                print(f"  ✅ {file_info['name']} - {file_info['size']} 字节")
                print(f"     修改时间: {file_info['modified']}")
        else:
            print(f"\n🎬 FBX 文件: 无")
        
    except Exception as e:
        print(f"❌ 列出文件失败: {e}")
        return False
    
    print("\n🎉 完整工作流程测试完成!")
    return True


def main():
    """主函数"""
    print("Motion Agent - 完整工作流程测试")
    print("验证 backend/output 目录功能")
    print("=" * 60)
    
    success = test_complete_workflow()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试成功完成!")
        print("\n✅ 验证结果:")
        print("- output 目录位于 backend/output")
        print("- JSON 文件正确保存到 backend/output")
        print("- FBX 文件路径指向 backend/output")
        print("- 所有文件都包含时间戳避免冲突")
        print("- 文件列表功能正常工作")
    else:
        print("❌ 测试过程中出现错误")
    
    output_dir = ensure_output_directory()
    print(f"\n📁 输出目录位置: {output_dir}")


if __name__ == "__main__":
    main()
