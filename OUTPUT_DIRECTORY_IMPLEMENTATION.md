# Output Directory Implementation

## 概述

已成功实现将转换的 JSON 和 .fbx 文件统一存储到 `backend/output` 文件夹中的需求。

## 实现的功能

### 1. 统一输出目录
- **位置**: `backend/output/`
- **自动创建**: 如果目录不存在，会自动创建
- **路径解析**: 使用相对路径自动定位到正确的 backend 目录

### 2. 文件管理功能

#### JSON 文件保存
- 动作命令 JSON (`*_actions_*.json`)
- Blender 项目 JSON (`*_blender_project_*.json`)
- 其他配置和测试 JSON 文件

#### FBX 文件保存
- 所有导出的 .fbx 文件都保存到 `backend/output/`
- 默认导出路径已更新为使用统一的输出目录

#### 时间戳功能
- 所有文件名都可以添加时间戳 (`YYYYMMDD_HHMMSS`)
- 避免文件名冲突
- 便于版本管理和追踪

### 3. API 增强

#### 新增端点
- `GET /output-files`: 列出输出目录中的所有文件
- `GET /output-files?file_type=json`: 只列出 JSON 文件
- `GET /output-files?file_type=fbx`: 只列出 FBX 文件
- `GET /download/{filename}`: 下载文件（支持 JSON 和 FBX）

#### 自动保存功能
- FBX 导出时自动保存对应的动作 JSON
- FBX 导出时自动保存 Blender 项目 JSON
- 所有文件都包含时间戳

### 4. 核心文件修改

#### `backend/utils/file.py`
```python
# 新增的核心函数
def ensure_output_directory(base_path: str = None) -> str
def save_json_to_output(data: Dict[str, Any], filename: str, output_dir: str = None, add_timestamp: bool = False) -> str
def get_output_file_path(filename: str, output_dir: str = None, add_timestamp: bool = False) -> str
def list_output_files(output_dir: str = None, file_extension: Optional[str] = None) -> list
```

#### `backend/app.py`
- 更新 FBX 导出端点，使用统一输出目录
- 添加自动保存 JSON 功能
- 新增文件列表和下载端点

#### `backend/blender/converter.py`
- 更新默认 FBX 导出路径为 `backend/output/`

#### `backend/blender/fbx_exporter.py`
- 添加自动保存 Blender 项目 JSON 功能

#### `backend/demo_workflow.py`
- 更新为使用新的输出目录结构

## 使用示例

### 1. 基本文件保存
```python
from backend.utils.file import save_json_to_output, get_output_file_path

# 保存 JSON 文件
json_path = save_json_to_output(data, "my_actions", add_timestamp=True)
# 结果: backend/output/my_actions_20250623_174852.json

# 获取 FBX 文件路径
fbx_path = get_output_file_path("animation.fbx", add_timestamp=True)
# 结果: backend/output/animation_20250623_174852.fbx
```

### 2. 列出文件
```python
from backend.utils.file import list_output_files

# 列出所有文件
all_files = list_output_files()

# 只列出 JSON 文件
json_files = list_output_files(file_extension=".json")

# 只列出 FBX 文件
fbx_files = list_output_files(file_extension=".fbx")
```

### 3. API 使用
```bash
# 列出所有输出文件
curl http://localhost:8000/output-files

# 只列出 JSON 文件
curl http://localhost:8000/output-files?file_type=json

# 下载文件
curl http://localhost:8000/download/my_animation.fbx -o animation.fbx
```

## 目录结构

```
motion-agent/
├── backend/
│   ├── output/                    # 统一输出目录
│   │   ├── demo_actions_20250623_174510.json
│   │   ├── demo_blender_project_20250623_174510.json
│   │   ├── test_actions_20250623_174451.json
│   │   ├── test_blender_project_20250623_174451.json
│   │   └── animation_20250623_174852.fbx
│   ├── utils/
│   │   └── file.py               # 文件管理工具
│   ├── app.py                    # API 服务器
│   ├── blender/
│   │   ├── converter.py          # Blender 转换器
│   │   └── fbx_exporter.py       # FBX 导出器
│   └── demo_workflow.py          # 演示工作流程
├── test_complete_workflow.py     # 完整测试脚本
└── OUTPUT_DIRECTORY_IMPLEMENTATION.md
```

## 测试验证

运行以下命令验证功能：

```bash
# 测试完整工作流程
python test_complete_workflow.py

# 测试输出目录功能
python test_output_directory.py

# 运行演示工作流程
python backend/demo_workflow.py

# 启动 API 服务器
python -m uvicorn backend.app:app --reload
```

## 特性总结

✅ **统一存储**: 所有 JSON 和 FBX 文件都保存在 `backend/output/`  
✅ **自动创建**: 输出目录自动创建，无需手动设置  
✅ **时间戳**: 文件名包含时间戳，避免冲突  
✅ **API 支持**: 提供文件列表和下载 API  
✅ **类型过滤**: 可按文件类型（JSON/FBX）过滤  
✅ **向后兼容**: 不影响现有功能  
✅ **测试覆盖**: 完整的测试脚本验证所有功能  

## 注意事项

1. **FBX 导出**: 当前 Blender 脚本存在问题，但路径配置正确
2. **权限**: 确保应用有权限在 `backend/output/` 目录创建文件
3. **磁盘空间**: FBX 文件可能较大，注意磁盘空间管理
4. **清理**: 可以定期清理旧的时间戳文件
