#!/usr/bin/env python3
"""
测试输出目录功能
验证 JSON 和 FBX 文件是否正确保存到 output 目录
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.utils.file import (
    save_json_to_output, get_output_file_path, ensure_output_directory, 
    list_output_files
)
from backend.agent.action_parser import ActionParser
from backend.blender.converter import GameActionToBlenderConverter
from backend.blender.fbx_exporter import FBXExporter


def test_output_directory_functionality():
    """测试输出目录功能"""
    print("🧪 测试输出目录功能")
    print("=" * 50)
    
    # 1. 测试基本文件操作
    print("\n📁 步骤 1: 测试基本文件操作")
    
    # 确保输出目录存在
    output_dir = ensure_output_directory()
    print(f"✅ 输出目录已创建: {output_dir}")
    
    # 测试保存 JSON 文件
    test_data = {
        "test": "data",
        "timestamp": "2024-01-01",
        "actions": [
            {"action": "test", "params": {"duration": 1.0}}
        ]
    }
    
    json_path = save_json_to_output(test_data, "test_file", add_timestamp=True)
    print(f"✅ 测试 JSON 文件已保存: {json_path}")
    
    # 测试获取文件路径
    fbx_path = get_output_file_path("test_animation.fbx", add_timestamp=True)
    print(f"✅ FBX 文件路径生成: {fbx_path}")
    
    # 2. 测试动作解析和保存
    print("\n🎯 步骤 2: 测试动作解析和保存")
    
    parser = ActionParser()
    natural_language = "人物挥手2秒，然后向前走3步"
    
    try:
        actions = parser.parse_simple_command(natural_language)
        print(f"✅ 解析成功，生成了 {len(actions)} 个动作")
        
        # 保存动作到输出目录
        actions_path = save_json_to_output(actions, "test_actions", add_timestamp=True)
        print(f"✅ 动作数据已保存: {actions_path}")
        
    except Exception as e:
        print(f"❌ 动作解析失败: {e}")
        return False
    
    # 3. 测试 Blender 转换和保存
    print("\n🎨 步骤 3: 测试 Blender 转换和保存")
    
    try:
        converter = GameActionToBlenderConverter()
        blender_project = converter.convert_actions_to_blender(actions, "TestProject")
        
        print(f"✅ Blender 转换成功")
        print(f"  项目名称: {blender_project.project_name}")
        print(f"  动画片段: {len(blender_project.animation_clips)}")
        
        # 保存 Blender 项目数据
        blender_path = save_json_to_output(
            blender_project.model_dump(), 
            "test_blender_project", 
            add_timestamp=True
        )
        print(f"✅ Blender 项目数据已保存: {blender_path}")
        
    except Exception as e:
        print(f"❌ Blender 转换失败: {e}")
        return False
    
    # 4. 测试 FBX 导出（如果 Blender 可用）
    print("\n📦 步骤 4: 测试 FBX 导出")
    
    try:
        exporter = FBXExporter()
        blender_info = exporter.get_blender_info()
        
        print(f"Blender 可用: {blender_info['available']}")
        
        if blender_info['available']:
            # 生成输出路径
            fbx_output_path = get_output_file_path("test_animation.fbx", add_timestamp=True)
            
            print("正在导出 FBX 文件...")
            result = exporter.export_actions_to_fbx(
                actions=actions,
                output_path=fbx_output_path,
                project_name="TestProject",
                character_name="TestCharacter"
            )
            
            if result["success"]:
                print(f"✅ FBX 导出成功: {result['output_path']}")
                print(f"  文件大小: {result.get('file_size', 0)} 字节")
            else:
                print(f"❌ FBX 导出失败: {result.get('error')}")
        else:
            print(f"⚠️ 跳过 FBX 导出（Blender 不可用）: {blender_info.get('error')}")
    
    except Exception as e:
        print(f"❌ FBX 导出异常: {e}")
    
    # 5. 列出输出目录中的文件
    print("\n📋 步骤 5: 列出输出目录中的文件")
    
    try:
        # 列出所有文件
        all_files = list_output_files()
        print(f"\n📁 输出目录: {output_dir}")
        print(f"📊 总文件数: {len(all_files)}")
        
        # 按类型分组显示
        json_files = list_output_files(file_extension=".json")
        fbx_files = list_output_files(file_extension=".fbx")
        
        print(f"\n📄 JSON 文件 ({len(json_files)} 个):")
        for file_info in json_files:
            print(f"  ✅ {file_info['name']} - {file_info['size']} 字节")
            print(f"     修改时间: {file_info['modified']}")
        
        if fbx_files:
            print(f"\n🎬 FBX 文件 ({len(fbx_files)} 个):")
            for file_info in fbx_files:
                print(f"  ✅ {file_info['name']} - {file_info['size']} 字节")
                print(f"     修改时间: {file_info['modified']}")
        else:
            print(f"\n🎬 FBX 文件: 无")
        
    except Exception as e:
        print(f"❌ 列出文件失败: {e}")
        return False
    
    print("\n🎉 输出目录功能测试完成!")
    return True


def main():
    """主函数"""
    print("Motion Agent - 输出目录功能测试")
    print("=" * 50)
    
    success = test_output_directory_functionality()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 测试成功完成!")
        print("\n💡 提示:")
        print("- 所有 JSON 和 FBX 文件现在都保存在 'output' 目录中")
        print("- 文件名包含时间戳以避免冲突")
        print("- 可以通过 API 端点 /output-files 查看所有文件")
        print("- 可以通过 /download/{filename} 下载文件")
    else:
        print("❌ 测试过程中出现错误")
    
    print(f"\n📁 输出目录位置: {os.path.abspath('output')}")


if __name__ == "__main__":
    main()
