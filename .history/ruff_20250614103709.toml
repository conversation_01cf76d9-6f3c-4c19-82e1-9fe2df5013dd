line-length = 120
indent-width = 4
target-version = "py311"

[lint]
select = ["E", "F", "I"]

[format]
quote-style = "double"
indent-style = "space"
line-ending = "auto"

# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".ipynb_checkpoints",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pyenv",
    ".pytest_cache",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    ".vscode",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "site-packages",
    "venv",
    "frontend/node_modules",
    "frontend/.next",
    "frontend/out",
    "frontend/build",
]
