"""
测试 Motion Agent 功能
"""

import os
import sys
import json
import asyncio

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.agent.animation_agent import GameActionAgent
from backend.agent.action_parser import ActionParser


def test_action_parser():
    """测试动作解析器"""
    print("=== 测试动作解析器 ===")
    
    parser = ActionParser()
    
    # 测试用例
    test_cases = [
        "人物挥手3秒，然后向前五步走，并跳跃",
        "角色向左移动10步，然后蹲下",
        "开心地跳跃，接着向右走3步",
        "愤怒地攻击敌人，然后防御"
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试用例 {i} ---")
        print(f"输入: {test_case}")
        
        try:
            # 使用规则解析器
            actions = parser.parse_simple_command(test_case)
            print(f"解析结果 ({len(actions)} 个动作):")
            print(json.dumps(actions, indent=2, ensure_ascii=False))
            
            # 验证结果
            validation = parser.validate_command_sequence(actions)
            print(f"验证结果: {'有效' if validation['valid'] else '无效'}")
            if validation['errors']:
                print(f"错误: {validation['errors']}")
            
        except Exception as e:
            print(f"解析失败: {e}")


async def test_game_agent():
    """测试游戏动作 Agent"""
    print("\n=== 测试游戏动作 Agent ===")
    
    # 检查 API Key
    if not os.getenv("OPENAI_API_KEY"):
        print("警告: OPENAI_API_KEY 环境变量未设置，跳过 LLM 测试")
        return
    
    try:
        agent = GameActionAgent()
        
        # 测试用例
        test_cases = [
            "人物挥手3秒，然后向前五步走，并跳跃",
            "角色先蹲下潜行，然后快速向前冲刺并攻击",
            "开心地跳舞5秒，接着拾取地上的物品"
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n--- LLM 测试用例 {i} ---")
            print(f"输入: {test_case}")
            
            try:
                # 异步解析
                result = await agent.aparse_natural_language(test_case)
                
                print(f"解析成功: {result['success']}")
                if result['success']:
                    print(f"生成动作数量: {len(result['actions'])}")
                    print("解析结果:")
                    print(json.dumps(result['actions'], indent=2, ensure_ascii=False))
                else:
                    print(f"解析失败: {result['error']}")
                
            except Exception as e:
                print(f"LLM 解析失败: {e}")
                
    except Exception as e:
        print(f"初始化 Agent 失败: {e}")


def test_supported_actions():
    """测试支持的动作列表"""
    print("\n=== 支持的动作列表 ===")
    
    try:
        parser = ActionParser()
        actions = parser.game_actions
        
        print(f"总共支持 {len(actions)} 个动作:")
        for action in actions[:5]:  # 只显示前5个
            print(f"- {action['name']} ({action['key']}): {action['desc']}")
        
        if len(actions) > 5:
            print(f"... 还有 {len(actions) - 5} 个动作")
            
    except Exception as e:
        print(f"获取动作列表失败: {e}")


def test_time_extraction():
    """测试时间提取功能"""
    print("\n=== 测试时间提取 ===")
    
    parser = ActionParser()
    
    test_texts = [
        "等待3秒",
        "持续5.5分钟",
        "运行2小时",
        "挥手3秒然后跳跃"
    ]
    
    for text in test_texts:
        time_info = parser.extract_time_info(text)
        print(f"'{text}' -> {time_info}")


def test_movement_extraction():
    """测试移动提取功能"""
    print("\n=== 测试移动提取 ===")
    
    parser = ActionParser()
    
    test_texts = [
        "向前走5步",
        "向左移动10步",
        "前进3米",
        "后退2步然后向右走"
    ]
    
    for text in test_texts:
        movements = parser.extract_movement_info(text)
        print(f"'{text}' -> {movements}")


async def main():
    """主测试函数"""
    print("Motion Agent 功能测试")
    print("=" * 50)
    
    # 测试基础功能
    test_supported_actions()
    test_time_extraction()
    test_movement_extraction()
    
    # 测试解析器
    test_action_parser()
    
    # 测试 LLM Agent
    await test_game_agent()
    
    print("\n" + "=" * 50)
    print("测试完成")


if __name__ == "__main__":
    # 设置测试环境
    import sys
    sys.path.append(".")
    
    # 运行测试
    asyncio.run(main())
