# main.py
import os
import json
from .agent.parser import GameActionParser

# 请确保在这里设置您的 OpenAI API Key
# 生产环境中推荐使用环境变量：export OPENAI_API_KEY="sk-..."
if not os.getenv("OPENAI_API_KEY"):
    # 仅作为演示，实际部署中不建议硬编码密钥
    print("Warning: OPENAI_API_KEY 环境变量未设置。请设置以运行。")
    # os.environ["OPENAI_API_KEY"] = "YOUR_OPENAI_API_KEY_HERE" # 如果需要测试，请在此处取消注释并替换

def run_parser_examples():
    """运行解析器示例。"""
    parser = GameActionParser(llm_model_name="gpt-4o", llm_temperature=0.1) # 可以调整模型和温度

    test_commands = [
        "人物向前移动5秒，速度是0.7。",
        "角色首先向左闪避，然后用剑攻击一下，接着跳起来再攻击。",
        "让人物表现出高兴的情绪，强度是0.9。同时UI界面闪烁一下，元素是健康条，持续2秒。",
        "人物执行一个攻击、攻击、防御的连击，时间间隔是0.5秒和0.8秒。",
        "请帮我预订明天去纽约的机票。", # 预期解析失败
        "拾取地上的钥匙，然后打开面前的门，方向是向外开。",
        "让角色装备魔法杖，并施放魔法攻击敌人。",
        "治疗受伤的队友，使用绷带。",
        "蹲下。",
        "角色向后翻滚，距离2.5米。",
        "驾驶坦克，全速前进。",
        "进入潜行模式，速度慢一点。",
        "查看背包里的物品。", # 预期解析为 LOOK/INTERACT (examine)
        "角色看向NPC '老王'。", # 新增look动作
        "丢弃身上的破旧木盾。", # 新增drop动作
        "使用背包里的治疗药水来治疗自己。" # 新增use_item动作
    ]

    for i, cmd in enumerate(test_commands):
        print(f"\n--- 示例 {i+1} ---")
        print(f"命令: \"{cmd}\"")
        
        parsed_actions = parser.parse_command(cmd)
        
        if parsed_actions:
            print("解析结果 (JSON):")
            print(json.dumps(parsed_actions, indent=2, ensure_ascii=False))
        else:
            print("未能解析出有效的游戏动作指令。")
        print("-" * 30)

if __name__ == "__main__":
    run_parser_examples()