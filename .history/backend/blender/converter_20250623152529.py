"""
游戏动作 JSON 到 Blender 动画数据转换器
"""

import json
import math
from typing import List, Dict, Any, Optional, Tuple
from backend.blender.animation_schema import (
    BlenderAnimationClip, BlenderAnimationChannel, BlenderKeyframe,
    BlenderProject, BlenderScene, BlenderExportSettings,
    BlenderAnimationType, InterpolationMode,
    ACTION_TO_BLENDER_MAPPING, DEFAULT_CHARACTER_BONES,
    seconds_to_frames, FRAME_RATE
)


class GameActionToBlenderConverter:
    """游戏动作到 Blender 动画转换器"""
    
    def __init__(self, character_name: str = "Character", frame_rate: float = FRAME_RATE):
        """初始化转换器
        
        Args:
            character_name: 角色名称
            frame_rate: 帧率
        """
        self.character_name = character_name
        self.frame_rate = frame_rate
        self.current_frame = 1
        self.animation_channels = []
        
    def convert_actions_to_blender(self, actions: List[Dict[str, Any]], 
                                 project_name: str = "MotionAgentAnimation") -> BlenderProject:
        """将游戏动作列表转换为 Blender 项目数据
        
        Args:
            actions: 游戏动作列表
            project_name: 项目名称
            
        Returns:
            BlenderProject: Blender 项目数据
        """
        # 重置状态
        self.current_frame = 1
        self.animation_channels = []
        
        # 转换每个动作
        for action in actions:
            self._convert_single_action(action)
        
        # 计算总帧数
        total_frames = max(self.current_frame, 50)  # 至少50帧
        
        # 创建动画片段
        animation_clip = BlenderAnimationClip(
            name=f"{project_name}_Animation",
            start_frame=1,
            end_frame=total_frames,
            frame_rate=self.frame_rate,
            channels=self.animation_channels
        )
        
        # 创建场景
        scene = BlenderScene(
            scene_name=f"{project_name}_Scene",
            frame_rate=self.frame_rate,
            frame_start=1,
            frame_end=total_frames
        )
        
        # 创建导出设置
        export_settings = BlenderExportSettings(
            export_path=f"/tmp/{project_name}.fbx",
            export_format="FBX"
        )
        
        # 创建项目
        project = BlenderProject(
            project_name=project_name,
            scene=scene,
            animation_clips=[animation_clip],
            export_settings=export_settings,
            metadata={
                "source": "MotionAgent",
                "actions_count": len(actions),
                "total_frames": total_frames
            }
        )
        
        return project
    
    def _convert_single_action(self, action: Dict[str, Any]) -> None:
        """转换单个动作
        
        Args:
            action: 单个游戏动作
        """
        action_type = action.get("action")
        params = action.get("params", {})
        
        if action_type == "move":
            self._convert_move_action(action, params)
        elif action_type == "jump":
            self._convert_jump_action(action, params)
        elif action_type == "emotion":
            self._convert_emotion_action(action, params)
        elif action_type == "idle":
            self._convert_idle_action(action, params)
        elif action_type == "attack":
            self._convert_attack_action(action, params)
        elif action_type == "defend":
            self._convert_defend_action(action, params)
        else:
            # 默认处理：添加一个简单的等待
            duration = params.get("duration", 1.0)
            self.current_frame += seconds_to_frames(duration, self.frame_rate)
    
    def _convert_move_action(self, action: Dict[str, Any], params: Dict[str, Any]) -> None:
        """转换移动动作"""
        direction = params.get("direction", "forward")
        speed = params.get("speed", 0.5)
        duration = params.get("duration", 1.0)
        
        # 获取方向映射
        direction_mapping = ACTION_TO_BLENDER_MAPPING["move"].get(direction)
        if not direction_mapping:
            return
        
        # 计算移动距离
        distance = speed * duration * 5.0  # 缩放因子
        
        # 创建关键帧
        start_frame = self.current_frame
        end_frame = self.current_frame + seconds_to_frames(duration, self.frame_rate)
        
        # 根据轴向创建动画通道
        axis = direction_mapping["axis"]
        multiplier = direction_mapping["multiplier"]
        
        keyframes = [
            BlenderKeyframe(frame=start_frame, value=[0, 0, 0]),
            BlenderKeyframe(frame=end_frame, value=[
                distance * multiplier if axis == "x" else 0,
                distance * multiplier if axis == "y" else 0,
                distance * multiplier if axis == "z" else 0
            ])
        ]
        
        channel = BlenderAnimationChannel(
            target_object=self.character_name,
            target_property="location",
            animation_type=BlenderAnimationType.LOCATION,
            keyframes=keyframes
        )
        
        self.animation_channels.append(channel)
        self.current_frame = end_frame
    
    def _convert_jump_action(self, action: Dict[str, Any], params: Dict[str, Any]) -> None:
        """转换跳跃动作"""
        height = params.get("height", 1.0)
        distance = params.get("distance", 1.0)
        style = params.get("style", "normal")
        
        # 跳跃持续时间（秒）
        jump_duration = 1.0 if style == "normal" else 1.5
        
        start_frame = self.current_frame
        peak_frame = self.current_frame + seconds_to_frames(jump_duration * 0.4, self.frame_rate)
        end_frame = self.current_frame + seconds_to_frames(jump_duration, self.frame_rate)
        
        # Z轴（高度）动画
        z_keyframes = [
            BlenderKeyframe(frame=start_frame, value=[0, 0, 0]),
            BlenderKeyframe(frame=peak_frame, value=[0, 0, height], 
                          interpolation=InterpolationMode.EASE_OUT),
            BlenderKeyframe(frame=end_frame, value=[0, 0, 0],
                          interpolation=InterpolationMode.EASE_IN)
        ]
        
        z_channel = BlenderAnimationChannel(
            target_object=self.character_name,
            target_property="location",
            animation_type=BlenderAnimationType.LOCATION,
            keyframes=z_keyframes
        )
        
        self.animation_channels.append(z_channel)
        
        # 如果有前进距离，添加Y轴动画
        if distance > 0:
            y_keyframes = [
                BlenderKeyframe(frame=start_frame, value=[0, 0, 0]),
                BlenderKeyframe(frame=end_frame, value=[0, distance, 0])
            ]
            
            y_channel = BlenderAnimationChannel(
                target_object=self.character_name,
                target_property="location",
                animation_type=BlenderAnimationType.LOCATION,
                keyframes=y_keyframes
            )
            
            self.animation_channels.append(y_channel)
        
        self.current_frame = end_frame
    
    def _convert_emotion_action(self, action: Dict[str, Any], params: Dict[str, Any]) -> None:
        """转换情绪动作"""
        emotion = params.get("emotion", "happy")
        intensity = params.get("intensity", 0.7)
        
        # 情绪持续时间
        duration = 2.0
        
        start_frame = self.current_frame
        end_frame = self.current_frame + seconds_to_frames(duration, self.frame_rate)
        
        # 获取情绪映射
        emotion_mapping = ACTION_TO_BLENDER_MAPPING["emotion"].get(emotion)
        if emotion_mapping and "shape_keys" in emotion_mapping:
            for shape_key, value in emotion_mapping["shape_keys"].items():
                keyframes = [
                    BlenderKeyframe(frame=start_frame, value=[0]),
                    BlenderKeyframe(frame=start_frame + 5, value=[value * intensity]),
                    BlenderKeyframe(frame=end_frame - 5, value=[value * intensity]),
                    BlenderKeyframe(frame=end_frame, value=[0])
                ]
                
                channel = BlenderAnimationChannel(
                    target_object=self.character_name,
                    target_property=f'key_blocks["{shape_key}"].value',
                    animation_type=BlenderAnimationType.SHAPE_KEY,
                    keyframes=keyframes
                )
                
                self.animation_channels.append(channel)
        
        self.current_frame = end_frame
    
    def _convert_idle_action(self, action: Dict[str, Any], params: Dict[str, Any]) -> None:
        """转换待机动作"""
        duration = params.get("duration", 1.0)
        mood = params.get("mood", "relaxed")
        
        # 简单的呼吸动画
        start_frame = self.current_frame
        end_frame = self.current_frame + seconds_to_frames(duration, self.frame_rate)
        
        # 轻微的上下浮动
        breath_amplitude = 0.02 if mood == "relaxed" else 0.01
        
        keyframes = []
        for frame in range(start_frame, end_frame, 6):  # 每6帧一个关键帧
            breath_offset = breath_amplitude * math.sin((frame - start_frame) * 0.2)
            keyframes.append(BlenderKeyframe(frame=frame, value=[0, 0, breath_offset]))
        
        if keyframes:
            channel = BlenderAnimationChannel(
                target_object=self.character_name,
                target_property="location",
                animation_type=BlenderAnimationType.LOCATION,
                keyframes=keyframes
            )
            
            self.animation_channels.append(channel)
        
        self.current_frame = end_frame
    
    def _convert_attack_action(self, action: Dict[str, Any], params: Dict[str, Any]) -> None:
        """转换攻击动作"""
        weapon = params.get("weapon", "fist")
        style = params.get("style", "slash")
        strength = params.get("strength", 0.8)
        
        # 攻击动作持续时间
        duration = 0.8
        
        start_frame = self.current_frame
        strike_frame = self.current_frame + seconds_to_frames(duration * 0.3, self.frame_rate)
        end_frame = self.current_frame + seconds_to_frames(duration, self.frame_rate)
        
        # 手臂旋转动画（简化版）
        rotation_amount = strength * 90  # 度数
        
        keyframes = [
            BlenderKeyframe(frame=start_frame, value=[0, 0, 0]),
            BlenderKeyframe(frame=strike_frame, value=[math.radians(rotation_amount), 0, 0],
                          interpolation=InterpolationMode.EASE_OUT),
            BlenderKeyframe(frame=end_frame, value=[0, 0, 0],
                          interpolation=InterpolationMode.EASE_IN)
        ]
        
        channel = BlenderAnimationChannel(
            target_object=self.character_name,
            target_property="rotation_euler",
            animation_type=BlenderAnimationType.ROTATION,
            keyframes=keyframes,
            bone_name="RightArm"
        )
        
        self.animation_channels.append(channel)
        self.current_frame = end_frame
    
    def _convert_defend_action(self, action: Dict[str, Any], params: Dict[str, Any]) -> None:
        """转换防御动作"""
        defend_type = params.get("type", "block")
        direction = params.get("direction", "front")
        
        # 防御动作持续时间
        duration = 1.5
        
        start_frame = self.current_frame
        end_frame = self.current_frame + seconds_to_frames(duration, self.frame_rate)
        
        # 防御姿态：轻微下蹲
        keyframes = [
            BlenderKeyframe(frame=start_frame, value=[0, 0, 0]),
            BlenderKeyframe(frame=start_frame + 10, value=[0, 0, -0.3]),
            BlenderKeyframe(frame=end_frame - 10, value=[0, 0, -0.3]),
            BlenderKeyframe(frame=end_frame, value=[0, 0, 0])
        ]
        
        channel = BlenderAnimationChannel(
            target_object=self.character_name,
            target_property="location",
            animation_type=BlenderAnimationType.LOCATION,
            keyframes=keyframes
        )
        
        self.animation_channels.append(channel)
        self.current_frame = end_frame
