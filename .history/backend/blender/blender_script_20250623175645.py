"""
Blender Python 脚本
用于在 Blender 中导入动画数据并生成动画序列
"""

import bpy
import bmesh
import json
import os
import sys
import mathutils
from typing import Dict, List, Any, Optional


class BlenderAnimationImporter:
    """Blender 动画导入器"""
    
    def __init__(self):
        """初始化导入器"""
        self.scene = bpy.context.scene
        self.character_object = None
        
    def clear_scene(self):
        """清空场景"""
        # 删除所有对象
        bpy.ops.object.select_all(action='SELECT')
        bpy.ops.object.delete(use_global=False)
        
        # 清空所有数据
        for block in bpy.data.meshes:
            bpy.data.meshes.remove(block, do_unlink=True)
        for block in bpy.data.materials:
            bpy.data.materials.remove(block, do_unlink=True)
        for block in bpy.data.actions:
            bpy.data.actions.remove(block, do_unlink=True)
    
    def create_character(self, name: str = "Character") -> bpy.types.Object:
        """创建角色对象
        
        Args:
            name: 角色名称
            
        Returns:
            角色对象
        """
        # 创建一个简单的立方体作为角色
        bpy.ops.mesh.primitive_cube_add(size=2, location=(0, 0, 1))
        character = bpy.context.active_object
        character.name = name
        
        # 添加材质
        material = bpy.data.materials.new(name=f"{name}_Material")
        material.use_nodes = True
        material.node_tree.nodes["Principled BSDF"].inputs[0].default_value = (0.2, 0.5, 0.8, 1.0)
        character.data.materials.append(material)
        
        self.character_object = character
        return character
    
    def create_armature(self, name: str = "Armature") -> bpy.types.Object:
        """创建骨架
        
        Args:
            name: 骨架名称
            
        Returns:
            骨架对象
        """
        # 创建骨架
        bpy.ops.object.armature_add(location=(0, 0, 0))
        armature = bpy.context.active_object
        armature.name = name
        
        # 进入编辑模式添加骨骼
        bpy.context.view_layer.objects.active = armature
        bpy.ops.object.mode_set(mode='EDIT')
        
        # 清除默认骨骼
        bpy.ops.armature.select_all(action='SELECT')
        bpy.ops.armature.delete()
        
        # 添加主要骨骼
        bones_data = [
            ("Root", (0, 0, 0), (0, 0, 0.5)),
            ("Spine", (0, 0, 0.5), (0, 0, 1.0)),
            ("Chest", (0, 0, 1.0), (0, 0, 1.5)),
            ("Neck", (0, 0, 1.5), (0, 0, 1.7)),
            ("Head", (0, 0, 1.7), (0, 0, 2.0)),
            ("LeftArm", (0, 0, 1.4), (-0.5, 0, 1.4)),
            ("RightArm", (0, 0, 1.4), (0.5, 0, 1.4)),
            ("LeftLeg", (0, 0, 0.5), (-0.3, 0, 0)),
            ("RightLeg", (0, 0, 0.5), (0.3, 0, 0))
        ]
        
        for bone_name, head, tail in bones_data:
            bone = armature.data.edit_bones.new(bone_name)
            bone.head = head
            bone.tail = tail
        
        # 设置骨骼层级关系
        armature.data.edit_bones["Spine"].parent = armature.data.edit_bones["Root"]
        armature.data.edit_bones["Chest"].parent = armature.data.edit_bones["Spine"]
        armature.data.edit_bones["Neck"].parent = armature.data.edit_bones["Chest"]
        armature.data.edit_bones["Head"].parent = armature.data.edit_bones["Neck"]
        armature.data.edit_bones["LeftArm"].parent = armature.data.edit_bones["Chest"]
        armature.data.edit_bones["RightArm"].parent = armature.data.edit_bones["Chest"]
        armature.data.edit_bones["LeftLeg"].parent = armature.data.edit_bones["Root"]
        armature.data.edit_bones["RightLeg"].parent = armature.data.edit_bones["Root"]
        
        bpy.ops.object.mode_set(mode='OBJECT')
        
        return armature
    
    def import_animation_data(self, animation_data: Dict[str, Any]) -> None:
        """导入动画数据
        
        Args:
            animation_data: Blender 项目动画数据
        """
        project_name = animation_data.get("project_name", "MotionAgentAnimation")
        scene_data = animation_data.get("scene", {})
        animation_clips = animation_data.get("animation_clips", [])
        
        # 设置场景属性
        self.scene.frame_start = scene_data.get("frame_start", 1)
        self.scene.frame_end = scene_data.get("frame_end", 250)
        self.scene.render.fps = int(scene_data.get("frame_rate", 24))
        
        # 创建角色和骨架
        character = self.create_character(project_name + "_Character")
        armature = self.create_armature(project_name + "_Armature")
        
        # 将角色绑定到骨架
        self._bind_character_to_armature(character, armature)
        
        # 导入动画片段
        for clip in animation_clips:
            self._import_animation_clip(clip, character, armature)
    
    def _bind_character_to_armature(self, character: bpy.types.Object, 
                                  armature: bpy.types.Object) -> None:
        """将角色绑定到骨架
        
        Args:
            character: 角色对象
            armature: 骨架对象
        """
        # 选择角色
        bpy.context.view_layer.objects.active = character
        character.select_set(True)
        armature.select_set(True)
        
        # 设置父级关系
        bpy.ops.object.parent_set(type='ARMATURE_AUTO')
        
        # 取消选择
        bpy.ops.object.select_all(action='DESELECT')
    
    def _import_animation_clip(self, clip_data: Dict[str, Any], 
                             character: bpy.types.Object,
                             armature: bpy.types.Object) -> None:
        """导入单个动画片段
        
        Args:
            clip_data: 动画片段数据
            character: 角色对象
            armature: 骨架对象
        """
        clip_name = clip_data.get("name", "Animation")
        channels = clip_data.get("channels", [])
        
        # 创建动作
        action = bpy.data.actions.new(name=clip_name)
        
        # 为每个通道创建关键帧
        for channel in channels:
            self._create_animation_channel(channel, action, character, armature)
        
        # 将动作分配给对象
        if channels:
            target_obj = character if channels[0].get("target_object") == character.name else armature
            if target_obj.animation_data is None:
                target_obj.animation_data_create()
            target_obj.animation_data.action = action
    
    def _create_animation_channel(self, channel_data: Dict[str, Any],
                                action: bpy.types.Action,
                                character: bpy.types.Object,
                                armature: bpy.types.Object) -> None:
        """创建动画通道
        
        Args:
            channel_data: 通道数据
            action: 动作对象
            character: 角色对象
            armature: 骨架对象
        """
        target_object = channel_data.get("target_object")
        target_property = channel_data.get("target_property")
        animation_type = channel_data.get("animation_type")
        keyframes = channel_data.get("keyframes", [])
        bone_name = channel_data.get("bone_name")
        
        # 确定目标对象
        if target_object == character.name:
            target_obj = character
        else:
            target_obj = armature
        
        # 创建数据路径
        if bone_name and animation_type in ["bone_rotation", "bone_location"]:
            data_path = f'pose.bones["{bone_name}"].{target_property}'
        else:
            data_path = target_property
        
        # 为每个轴创建 F-Curve
        if animation_type in ["location", "rotation", "scale"]:
            axes = ["x", "y", "z"]
        else:
            axes = ["value"]
        
        for axis_index, axis in enumerate(axes):
            if axis_index < len(keyframes[0].get("value", [])) if keyframes else True:
                fcurve = action.fcurves.new(data_path=data_path, index=axis_index)
                
                # 添加关键帧
                for keyframe in keyframes:
                    frame = keyframe.get("frame", 1)
                    value = keyframe.get("value", [0, 0, 0])
                    
                    if axis_index < len(value):
                        kf = fcurve.keyframe_points.insert(frame, value[axis_index])
                        
                        # 设置插值模式
                        interpolation = keyframe.get("interpolation", "LINEAR")
                        if interpolation == "BEZIER":
                            kf.interpolation = 'BEZIER'
                        elif interpolation == "CONSTANT":
                            kf.interpolation = 'CONSTANT'
                        else:
                            kf.interpolation = 'LINEAR'
    
    def export_fbx(self, filepath: str, export_settings: Dict[str, Any] = None) -> bool:
        """导出 FBX 文件
        
        Args:
            filepath: 导出路径
            export_settings: 导出设置
            
        Returns:
            是否成功
        """
        try:
            # 默认导出设置
            settings = {
                'use_selection': False,
                'use_active_collection': False,
                'global_scale': export_settings.get('scale_factor', 1.0) if export_settings else 1.0,
                'apply_unit_scale': True,
                'apply_scale_options': 'FBX_SCALE_NONE',
                'use_space_transform': True,
                'bake_space_transform': False,
                'object_types': {'ARMATURE', 'MESH'},
                'use_mesh_modifiers': True,
                'use_mesh_modifiers_render': True,
                'mesh_smooth_type': 'OFF',
                'use_subsurf': False,
                'use_mesh_edges': False,
                'use_tspace': False,
                'use_custom_props': False,
                'add_leaf_bones': True,
                'primary_bone_axis': 'Y',
                'secondary_bone_axis': 'X',
                'use_armature_deform_only': False,
                'armature_nodetype': 'NULL',
                'bake_anim': True,
                'bake_anim_use_all_bones': True,
                'bake_anim_use_nla_strips': True,
                'bake_anim_use_all_actions': False,
                'bake_anim_force_startend_keying': True,
                'bake_anim_step': 1.0,
                'bake_anim_simplify_factor': 1.0,
                'path_mode': 'AUTO',
                'embed_textures': False,
                'batch_mode': 'OFF',
                'use_batch_own_dir': True,
                'use_metadata': True
            }
            
            # 更新设置
            if export_settings:
                settings.update(export_settings)
            
            # 导出 FBX
            bpy.ops.export_scene.fbx(filepath=filepath, **settings)
            
            print(f"FBX 文件已导出到: {filepath}")
            return True
            
        except Exception as e:
            print(f"导出 FBX 失败: {e}")
            return False


def main(animation_data_path: str, output_path: str) -> bool:
    """主函数
    
    Args:
        animation_data_path: 动画数据文件路径
        output_path: 输出 FBX 文件路径
        
    Returns:
        是否成功
    """
    try:
        # 读取动画数据
        with open(animation_data_path, 'r', encoding='utf-8') as f:
            animation_data = json.load(f)
        
        # 创建导入器
        importer = BlenderAnimationImporter()
        
        # 清空场景
        importer.clear_scene()
        
        # 导入动画数据
        importer.import_animation_data(animation_data)
        
        # 导出 FBX
        export_settings = animation_data.get("export_settings", {})
        success = importer.export_fbx(output_path, export_settings)
        
        if success:
            print("动画导入和导出完成!")
            return True
        else:
            print("导出失败!")
            return False
            
    except Exception as e:
        print(f"处理失败: {e}")
        return False


if __name__ == "__main__":
    # 从命令行参数获取路径
    # 当使用 blender --python script.py -- arg1 arg2 时，
    # sys.argv 包含所有 Blender 参数，需要找到 '--' 后的参数

    try:
        # 找到 '--' 分隔符的位置
        separator_index = sys.argv.index('--')
        script_args = sys.argv[separator_index + 1:]

        if len(script_args) >= 2:
            animation_data_path = script_args[0]
            output_path = script_args[1]
            main(animation_data_path, output_path)
        else:
            print("错误: 需要提供动画数据文件路径和输出文件路径")
            print("用法: blender --background --python blender_script.py -- <animation_data.json> <output.fbx>")
    except ValueError:
        print("错误: 未找到 '--' 分隔符")
        print("用法: blender --background --python blender_script.py -- <animation_data.json> <output.fbx>")
    except Exception as e:
        print(f"参数解析错误: {e}")
        print("用法: blender --background --python blender_script.py -- <animation_data.json> <output.fbx>")
