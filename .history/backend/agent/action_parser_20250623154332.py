"""
游戏动作解析器工具类
提供便捷的接口来解析自然语言为游戏动作命令
"""

import json
import re
from typing import List, Dict, Any, Optional, Tuple
from backend.utils.file import load_json_file


class ActionParser:
    """动作解析器辅助类"""
    
    def __init__(self):
        """初始化解析器"""
        self.game_actions = load_json_file("backend/actions/game_action_command_set.json")
        self.action_map = {action["name"]: action for action in self.game_actions}
        self.key_map = {action["key"]: action for action in self.game_actions}
    
    def extract_time_info(self, text: str) -> List[Tuple[str, float]]:
        """从文本中提取时间信息
        
        Args:
            text: 输入文本
            
        Returns:
            时间信息列表 [(描述, 时间值)]
        """
        time_patterns = [
            (r'(\d+(?:\.\d+)?)\s*秒', lambda m: float(m.group(1))),
            (r'(\d+(?:\.\d+)?)\s*分钟', lambda m: float(m.group(1)) * 60),
            (r'(\d+(?:\.\d+)?)\s*小时', lambda m: float(m.group(1)) * 3600),
        ]
        
        time_info = []
        for pattern, converter in time_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                duration = converter(match)
                time_info.append((match.group(0), duration))
        
        return time_info
    
    def extract_movement_info(self, text: str) -> List[Dict[str, Any]]:
        """从文本中提取移动信息
        
        Args:
            text: 输入文本
            
        Returns:
            移动信息列表
        """
        movement_patterns = {
            r'向前.*?(\d+)\s*步': {"direction": "forward", "type": "steps"},
            r'向后.*?(\d+)\s*步': {"direction": "backward", "type": "steps"},
            r'向左.*?(\d+)\s*步': {"direction": "left", "type": "steps"},
            r'向右.*?(\d+)\s*步': {"direction": "right", "type": "steps"},
            r'前进.*?(\d+)\s*米': {"direction": "forward", "type": "meters"},
            r'后退.*?(\d+)\s*米': {"direction": "backward", "type": "meters"},
        }
        
        movements = []
        for pattern, info in movement_patterns.items():
            matches = re.finditer(pattern, text)
            for match in matches:
                distance = float(match.group(1))
                movements.append({
                    "direction": info["direction"],
                    "distance": distance,
                    "type": info["type"],
                    "text": match.group(0)
                })
        
        return movements
    
    def extract_emotions(self, text: str) -> List[str]:
        """从文本中提取情绪信息
        
        Args:
            text: 输入文本
            
        Returns:
            情绪列表
        """
        emotion_keywords = {
            "挥手": "happy",
            "微笑": "happy", 
            "开心": "happy",
            "高兴": "happy",
            "愤怒": "angry",
            "生气": "angry",
            "伤心": "sad",
            "难过": "sad",
            "惊讶": "surprised",
            "害怕": "scared",
            "恐惧": "scared",
            "困惑": "confused",
            "自信": "confident"
        }
        
        emotions = []
        for keyword, emotion in emotion_keywords.items():
            if keyword in text:
                emotions.append(emotion)
        
        return list(set(emotions))  # 去重
    
    def extract_actions(self, text: str) -> List[str]:
        """从文本中提取动作关键词
        
        Args:
            text: 输入文本
            
        Returns:
            动作关键词列表
        """
        action_keywords = {
            "跳跃": "jump",
            "跳": "jump",
            "攻击": "attack",
            "打击": "attack",
            "防御": "defend",
            "阻挡": "defend",
            "蹲下": "crouch",
            "潜行": "sneak",
            "翻滚": "roll",
            "治疗": "heal",
            "拾取": "pick_up",
            "丢弃": "drop",
            "装备": "equip",
            "卸下": "unequip",
            "打开": "open",
            "关闭": "close",
            "对话": "talk",
            "交谈": "talk"
        }
        
        actions = []
        for keyword, action in action_keywords.items():
            if keyword in text:
                actions.append(action)
        
        return list(set(actions))  # 去重
    
    def create_action_command(self, action_name: str, params: Dict[str, Any],
                            sequence: int, description_en: str = "", description_zh: str = "") -> Dict[str, Any]:
        """创建动作命令

        Args:
            action_name: 动作名称
            params: 参数字典
            sequence: 序列号
            description_en: 英文描述
            description_zh: 中文描述

        Returns:
            动作命令字典
        """
        if action_name not in self.action_map:
            raise ValueError(f"不支持的动作: {action_name}")

        action_def = self.action_map[action_name]

        return {
            "action_type": action_name,
            "action_key": action_def["key"],
            "parameters": params,
            "sequence_number": sequence,
            "description_en": description_en or action_def["desc"],
            "description_zh": description_zh or action_def["desc"]
        }
    
    def parse_simple_command(self, text: str) -> List[Dict[str, Any]]:
        """简单解析命令（不使用 LLM）
        
        Args:
            text: 输入文本
            
        Returns:
            动作命令列表
        """
        commands = []
        sequence = 1
        
        # 提取时间信息
        time_info = self.extract_time_info(text)
        
        # 提取情绪
        emotions = self.extract_emotions(text)
        if emotions:
            for emotion in emotions:
                commands.append(self.create_action_command(
                    "emotion",
                    {"emotion": emotion, "intensity": 0.7},
                    sequence,
                    f"表达{emotion}情绪"
                ))
                sequence += 1
        
        # 提取移动
        movements = self.extract_movement_info(text)
        for movement in movements:
            duration = 5.0  # 默认持续时间
            if time_info:
                duration = time_info[0][1]  # 使用第一个时间信息
            
            commands.append(self.create_action_command(
                "move",
                {
                    "direction": movement["direction"],
                    "speed": 0.5,
                    "duration": duration
                },
                sequence,
                f"向{movement['direction']}移动{movement['distance']}{movement['type']}"
            ))
            sequence += 1
        
        # 提取其他动作
        actions = self.extract_actions(text)
        for action in actions:
            if action == "jump":
                commands.append(self.create_action_command(
                    "jump",
                    {"height": 1.0, "distance": 1.0, "style": "normal"},
                    sequence,
                    "跳跃动作"
                ))
                sequence += 1
        
        return commands
    
    def validate_command_sequence(self, commands: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证命令序列
        
        Args:
            commands: 命令列表
            
        Returns:
            验证结果
        """
        errors = []
        warnings = []
        
        for i, cmd in enumerate(commands):
            # 检查必需字段
            required_fields = ["action", "key", "params", "sequence", "description"]
            for field in required_fields:
                if field not in cmd:
                    errors.append(f"命令 {i+1} 缺少字段: {field}")
            
            # 检查动作是否支持
            if cmd.get("action") not in self.action_map:
                errors.append(f"命令 {i+1} 不支持的动作: {cmd.get('action')}")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "command_count": len(commands)
        }
