from langchain_core.utils.function_calling import convert_to_openai_function
from langchain_core.prompts import ChatPromptTemplate, SystemMessagePromptTemplate, HumanMessagePromptTemplate
from langgraph.prebuilt import create_react_agent
from langchain.chat_models import init_chat_model

from backend.utils.file import load_json_file


game_actions = load_json_file("backend/actions/game_action_command_set.json")   


def extract_actions_from_text(text: str, game_actions: list) -> list:
    """Extracts actions from the input text based on the game actions."""
    actions = []
    for action in game_actions:
        action_name = action["name"]
        action_key = action["key"]
        action_desc = action["desc"]
        params = action["params"]
        # 使用更灵活的匹配方式，包括动作名称、key和描述
        if re.search(r"\b" + action_name + r"\b|\b" + action_key + r"\b", text, re.IGNORECASE) or re.search(action_desc, text, re.IGNORECASE):
            extracted_params = {}
            for param_name, param_type in params.items():
                # 尝试从文本中提取参数值 (非常简化的提取逻辑，需要根据实际情况改进)
                match = re.search(r"" + param_name + r"\s*[:=]\s*(\w+)", text, re.IGNORECASE)  # 提取 key value
                if match:
                    extracted_params[param_name] = match.group(1)  # 保存参数值
            actions.append({"name": action_name, "params": extracted_params})
    return actions
\

async def animation_agent(user_input: str):
    llm = init_chat_model(model="gpt-4o", temperature=0)

    prompt = ChatPromptTemplate.from_messages([
        SystemMessagePromptTemplate("你是一个动画指令助手。"),
        HumanMessagePromptTemplate("{user_input}")
    ])

    agent = create_react_agent(name="animation-agent", model=llm)
    return await agent.arun(user_input)

if __name__ == "__main__":
    user_input = "一个人物先挥手3秒，然后跳跃一下。"
    parsed_sequence = animation_agent(user_input)
    print(parsed_sequence)
