import json
import re
from typing import List, Dict, Any, Optional
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import JsonOutputParser
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from typing_extensions import TypedDict
from langchain_core.messages import HumanMessage, AIMessage

from backend.utils.file import load_json_file


class AgentState(TypedDict):
    """Agent 状态定义"""
    messages: List[Any]
    natural_language_input: str
    parsed_actions: List[Dict[str, Any]]
    error: Optional[str]


class GameActionAgent:
    """游戏动作解析 Agent"""

    def __init__(self, model_name: str = "gpt-4o-mini", temperature: float = 0.1):
        """初始化 Agent

        Args:
            model_name: 使用的模型名称
            temperature: 模型温度参数
        """
        self.llm = ChatOpenAI(model=model_name, temperature=temperature)
        self.game_actions = load_json_file("backend/actions/game_action_command_set.json")
        self.parser = JsonOutputParser()

        # 创建 LangGraph
        self.graph = self._create_graph()

    def _create_action_schema_prompt(self) -> str:
        """创建动作模式的提示文本"""
        schema_text = "可用的游戏动作命令:\n\n"

        for action in self.game_actions:
            schema_text += f"**{action['name']} ({action['key']})**\n"
            schema_text += f"描述: {action['desc']}\n"
            schema_text += "参数:\n"

            for param, desc in action['params'].items():
                schema_text += f"  - {param}: {desc}\n"
            schema_text += "\n"

        return schema_text

    def _create_system_prompt(self) -> str:
        """创建系统提示"""
        action_schema = self._create_action_schema_prompt()

        return f"""你是一个专业的游戏动作解析器。你的任务是将自然语言描述转换为结构化的游戏动作命令序列。

{action_schema}

**解析规则:**
1. 仔细分析输入的自然语言，识别所有动作和时间序列
2. 将每个动作映射到对应的游戏命令
3. 保持动作的时间顺序和逻辑关系
4. 为每个动作填充合适的参数值
5. 输出格式必须是有效的 JSON 数组

**输出格式:**
```json
[
  {{
    "action": "动作名称",
    "key": "动作键",
    "params": {{
      "参数名": "参数值"
    }},
    "sequence": 序号,
    "description": "动作描述"
  }}
]
```

**示例:**
输入: "人物挥手3秒，然后向前五步走，并跳跃"
输出:
```json
[
  {{
    "action": "emotion",
    "key": "EMOTION",
    "params": {{
      "emotion": "happy",
      "intensity": 0.7
    }},
    "sequence": 1,
    "description": "挥手表达情绪"
  }},
  {{
    "action": "idle",
    "key": "IDLE",
    "params": {{
      "mood": "alert",
      "duration": 3.0
    }},
    "sequence": 2,
    "description": "挥手动作持续3秒"
  }},
  {{
    "action": "move",
    "key": "MOVE",
    "params": {{
      "direction": "forward",
      "speed": 0.5,
      "duration": 5.0
    }},
    "sequence": 3,
    "description": "向前走五步"
  }},
  {{
    "action": "jump",
    "key": "JUMP",
    "params": {{
      "height": 1.0,
      "distance": 1.0,
      "style": "normal"
    }},
    "sequence": 4,
    "description": "跳跃动作"
  }}
]
```

请严格按照上述格式输出，确保 JSON 格式正确且包含所有必要字段。"""