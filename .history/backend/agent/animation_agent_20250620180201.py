import logging

import dotenv
from langchain.tools import tool
from langchain_core.prompts import (
    ChatPromptTemplate,
    HumanMessagePromptTemplate,
    SystemMessagePromptTemplate,
)

logger = logging.getLogger(__name__)


@tool
async def text_to_animation_direction(
    text: str,
) -> str:
    """
    将自然语言转换为动画指令或动作序列

    Args:
        text: 输入的自然语言
    """
    system_prompt = SystemMessagePromptTemplate.from_template(
        "你是一个专业动画指令生成器，将自然语言转换为JSON格式的动作指令序列"
    )
    human_prompt = HumanMessagePromptTemplate.from_template("{text}")
    prompt = ChatPromptTemplate.from_messages([system_prompt, human_prompt])
    logger.info(f"Generating 3D animation from text: {text}")
    return "https://example.com/animation.fbx"
