# game_action_parser/agent.py
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage, ToolMessage, BaseMessage
from langchain_core.utils.function_calling import convert_to_openai_function
from langgraph.graph import StateGraph, END
from typing import TypedDict, List, Dict, Any, Union, Optional
import os

from backend.actions.models import ExecuteAction # 导入我们定义的模型

# 设置 OpenAI API Key
# 最佳实践是在环境变量中设置，而不是硬编码
# os.environ["OPENAI_API_KEY"] = "YOUR_OPENAI_API_KEY" 

class AgentState(TypedDict):
    """
    LangGraph 代理的状态定义。
    messages: 存储对话历史。
    parsed_actions: 存储成功解析的动作序列。
    """
    messages: List[BaseMessage]
    parsed_actions: Optional[List[Dict[str, Any]]]

def create_game_action_agent(model_name: str = "gpt-4o", temperature: float = 0.0):
    """
    创建并编译 LangGraph 代理，用于解析游戏动作指令。
    """
    llm = ChatOpenAI(model=model_name, temperature=temperature)

    # 将 ExecuteAction Pydantic 模型转换为 OpenAI 函数定义
    execute_action_tool = convert_to_openai_function(ExecuteAction)
    llm_with_tools = llm.bind_functions([execute_action_tool])

    #### 定义节点 ####

    # 1. 调用 LLM 的节点
    def call_llm(state: AgentState) -> AgentState:
        """调用 LLM 并将 LLM 的响应添加到消息历史中。"""
        messages = state["messages"]
        response = llm_with_tools.invoke(messages)
        return {"messages": messages + [response]}

    # 2. 解析工具调用的节点
    def parse_tool_call(state: AgentState) -> AgentState:
        """解析 LLM 返回的工具调用，并验证动作参数。"""
        messages = state["messages"]
        last_message = messages[-1]

        if not last_message.tool_calls:
            # 如果 LLM 没有进行工具调用，说明它没有理解为动作指令
            return {
                "parsed_actions": None,
                "messages": messages + [
                    SystemMessage("未能识别有效的游戏动作指令。请尝试更明确的描述。")
                ]
            }

        # 假设只有一个工具调用
        tool_call = last_message.tool_calls[0] 
        tool_name = tool_call["name"]
        tool_args = tool_call["args"]

        if tool_name == "ExecuteAction":
            try:
                # 使用 Pydantic 模型解析工具参数
                action_sequence_model = ExecuteAction(**tool_args)
                
                # 验证解析出的动作参数
                validated_actions = action_sequence_model.validate_actions_list()

                return {
                    "parsed_actions": validated_actions,
                    "messages": messages + [
                        SystemMessage(f"成功解析指令为以下动作序列: {validated_actions}")
                    ]
                }
            except Exception as e:
                # 如果解析失败，返回错误信息
                error_message = f"解析动作参数失败: {e}. 请检查指令是否符合规范。"
                return {
                    "parsed_actions": None,
                    "messages": messages + [
                        ToolMessage(content=error_message, tool_call_id=tool_call["id"])
                    ]
                }
        else:
            # 如果调用了其他未定义的工具（不应发生，因为只绑定了一个）
            error_message = f"调用了未知的工具: {tool_name}. 请只使用 ExecuteAction。"
            return {
                "parsed_actions": None,
                "messages": messages + [ToolMessage(content=error_message, tool_call_id=tool_call["id"])]
            }

    #### 定义条件边 (Conditional Edges) ####
    def decide_next_step(state: AgentState) -> str:
        """根据是否成功解析动作来决定下一步流程。"""
        if state.get("parsed_actions"):
            return "end_success"
        else:
            return "end_failure"

    #### 构建 LangGraph 图 ####
    workflow = StateGraph(AgentState)

    workflow.add_node("call_llm", call_llm)
    workflow.add_node("parse_tool_call", parse_tool_call)

    workflow.set_entry_point("call_llm")

    workflow.add_edge("call_llm", "parse_tool_call")

    workflow.add_conditional_edges(
        "parse_tool_call",
        decide_next_step,
        {
            "end_success": END, # 成功解析，结束图
            "end_failure": END  # 解析失败，结束图（简化处理，实际可循环让LLM重试）
        }
    )

    return workflow.compile()