# game_action_parser/parser.py
from typing import List, Dict, Any, Optional
from langchain_core.messages import HumanMessage
import os

# 从 agent.py 导入创建代理的函数
from .animation_agent import create_game_action_agent, AgentState

class GameActionParser:
    def __init__(self, llm_model_name: str = "gpt-4o", llm_temperature: float = 0.0):
        """
        初始化游戏动作解析器。
        Args:
            llm_model_name (str): 用于解析的 LLM 模型名称 (e.g., "gpt-4o").
            llm_temperature (float): LLM 的温度参数，0.0 表示更确定性的输出。
        """
        self.agent_app = create_game_action_agent(model_name=llm_model_name, temperature=llm_temperature)

    def parse_command(self, command: str) -> Optional[List[Dict[str, Any]]]:
        """
        解析自然语言命令为结构化的游戏动作序列。
        Args:
            command (str): 用户的自然语言指令，例如 "人物向前移动5秒并跳跃"。
        Returns:
            Optional[List[Dict[str, Any]]]: 
                如果成功解析，返回一个动作字典列表，每个字典包含 'type' 和 'params'。
                如果解析失败或未识别有效动作，返回 None。
        """
        initial_state = {"messages": [HumanMessage(content=command)], "parsed_actions": None}
        
        try:
            # 调用 LangGraph 代理
            final_state: AgentState = self.agent_app.invoke(initial_state)
            
            # 返回解析出的动作，如果存在
            return final_state.get("parsed_actions")
        except Exception as e:
            print(f"解析命令时发生错误: {e}")
            # 打印详细的 LangGraph 消息历史以供调试
            # if 'final_state' in locals():
            #     print("LangGraph 消息历史:")
            #     for msg in final_state.get("messages", []):
            #         print(f"  {msg}")
            return None