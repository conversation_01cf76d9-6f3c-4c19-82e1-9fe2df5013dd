from langchain_core.utils.function_calling import convert_to_openai_function
from langchain_core.prompts import ChatPromptTemplate, SystemMessagePromptTemplate, HumanMessagePromptTemplate
from langgraph.prebuilt import create_react_agent
from langchain.chat_models import init_chat_model

import re
import json
from typing import List, Dict, Any
from langchain.agents import create_react_agent
from langchain_core.prompts import PromptTemplate
from langchain.tools import Tool
from langchain_core.runnables import chain
from langchain.chains.llm import LLMChain
from langchain_openai import ChatOpenAI
from langchain_core.output_parsers import JsonOutputParser

from backend.utils.file import load_json_file


game_actions = load_json_file("backend/actions/game_action_command_set.json")



def extract_actions_from_text(text: str, game_actions: list) -> list:
    """Extracts actions from the input text based on the game actions."""
    actions = []
    for action in game_actions:
        action_name = action["name"]
        action_key = action["key"]
        action_desc = action["desc"]
        params = action["params"]
        # 使用更灵活的匹配方式，包括动作名称、key和描述
        if re.search(r"\b" + action_name + r"\b|\b" + action_key + r"\b", text, re.IGNORECASE) or re.search(action_desc, text, re.IGNORECASE):
            extracted_params = {}
            for param_name, param_type in params.items():
                # 尝试从文本中提取参数值 (非常简化的提取逻辑，需要根据实际情况改进)
                match = re.search(r"" + param_name + r"\s*[:=]\s*(\w+)", text, re.IGNORECASE)  # 提取 key value
                if match:
                    extracted_params[param_name] = match.group(1)  # 保存参数值
            actions.append({"name": action_name, "params": extracted_params})
    return actions

def perform_action(action_name: str, params: Dict[str, Any]) -> str:
    """Performs a specific animation action."""
    # 在这里添加实际的动画控制代码
    # 例如，调用一个游戏引擎的 API 来执行动作
    # 这里只是一个示例，返回一个简单的字符串
    return f"执行动作: {action_name}, 参数: {params}"
tools = [
    Tool(
        name=action["name"],  # 工具名称使用动作名称
        func=lambda action_input: perform_action(action["name"], action_input),  # 使用 perform_action 函数来执行动作
        description=f"执行一个 {action['desc']} 动作.  需要参数: {action['params']}",  # 工具描述包含动作描述和参数信息
    )
    for action in game_actions  # 遍历所有动作，创建工具
]

async def animation_agent(user_input: str):

    prompt = PromptTemplate.from_template("""你是一个游戏指令助手，负责解析用户输入的动作指令，并调用相应的工具来执行动作。
你可以使用的工具如下：
{tools}
请根据用户的指令，选择合适的工具来执行动作。
请使用如下的JSON结构返回：
```json
{{{{ "action": tool_name, "action_input": parameters }}}}
用户的指令是：{input}""")
    
    tools_string = "\n".join([f"{tool.name}: {tool.description}" for tool in tools])
    prompt = prompt.partial(tools=tools_string)

    llm = init_chat_model(model="gpt-4o", temperature=0)

    agent = create_react_agent(name="animation-agent", model=llm)
    tool_names = [tool.name for tool in tools]
    parser = JsonOutputParser()

    @chain
    def runnable_agent(input: str):
        """The agent chain."""
        intermediate_step = llm.invoke(input)
        action = parser.parse(intermediate_step["text"])
        if action["action"] in tool_names:
            tool_output = next(
            (tool.run(action["action_input"]) for tool in tools if tool.name == action["action"]),
            "工具未找到",
            )
            return {"tool_output": tool_output}
        else:
            return "工具选择错误"

    return runnable_agent.invoke({"input": instruction})

if __name__ == "__main__":
    user_input = "一个人物先挥手3秒，然后跳跃一下。"
    parsed_sequence = animation_agent(user_input)
    print(parsed_sequence)
