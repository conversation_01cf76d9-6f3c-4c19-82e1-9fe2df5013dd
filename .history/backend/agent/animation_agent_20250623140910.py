from langchain_core.utils.function_calling import convert_to_openai_function
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langgraph.prebuilt import create_react_agent
from langchain.chat_models import init_chat_model

# 将 Pydantic 模型转换为 OpenAI 函数定义
animation_sequence_function = convert_to_openai_function(AnimationSequence)

prompt = ChatPromptTemplate.from_messages(
    [
        ("system", "你是一个动画指令助手。"),
        ("human", "{query}")
    ]
)

# 绑定函数工具到 LLM
llm_with_tools = llm.bind_functions([animation_sequence_function])

# 创建一个输出解析器来解析函数调用
from langchain.output_parsers.openai_functions import JsonOutputFunctionsParser
parser = JsonOutputFunctionsParser(args_only=False) # args_only=True 如果只需要函数的参数

chain = prompt | llm_with_tools | parser

# 示例解析
user_input = "一个人物先挥手3秒，然后跳跃一下。"
parsed_sequence = chain.invoke({"query": user_input})
print(parsed_sequence)

user_input_2 = "角色向左转，接着向前跑。"
parsed_sequence_2 = chain.invoke({"query": user_input_2})
print(parsed_sequence_2)

# 注意：Function Calling 的输出是字典，其中 'args' 键包含了你定义的 AnimationSequence 对象。
# 所以你需要从 parsed_sequence['args'] 中获取实际的 Pydantic 对象。
from pydantic import parse_obj_as
parsed_animation_obj = parse_obj_as(AnimationSequence, parsed_sequence['args'])
print(parsed_animation_obj.model_dump_json(indent=2)) # Pydantic v2 .model_dump_json()

async def animation_agent(user_input: str):
    llm = init_chat_model(model="gpt-4o", temperature=0) 
    agent = create_react_agent(name="animation-agent", model=)
    return await agent.arun(user_input)

if __name__ == "__main__":
    user_input = "一个人物先挥手3秒，然后跳跃一下。"
    parsed_sequence = animation_agent(user_input)
    print(parsed_sequence)
