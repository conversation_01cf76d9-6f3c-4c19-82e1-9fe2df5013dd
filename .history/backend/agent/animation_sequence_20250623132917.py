from pydantic import BaseModel, Field
from typing import List, Literal, Optional

# 定义一个单一的动画动作
class AnimationAction(BaseModel):
    action_type: Literal["wave", "jump", "walk", "run", "turn", "idle"] = Field(
        ..., description="人物要执行的动作类型，限定在预设列表内。"
    )
    duration_seconds: Optional[int] = Field(
        None, description="动作的持续时间（秒），如果不需要持续时间则为None。"
    )
    magnitude: Optional[Literal["small", "medium", "large"]] = Field(
        None, description="动作的幅度或强度，例如'jump'可以是'small'跳跃或'large'跳跃。"
    )
    direction: Optional[Literal["left", "right", "forward", "backward"]] = Field(
        None, description="动作的方向，例如'turn'可以是'left'。"
    )

# 定义整个动画序列
class AnimationSequence(BaseModel):
    subject: Literal["person", "character", "human"] = Field(
        "person", description="动画的主体，默认为'person'。"
    )
    actions: List[AnimationAction] = Field(
        ..., description="一系列按顺序执行的动画动作。"
    )