"""
Motion Agent Backend API
提供自然语言到游戏动作命令转换的 REST API 服务
"""

import os
import asyncio
from typing import List, Dict, Any, Optional
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from loguru import logger
import uvicorn

from backend.agent.animation_agent import GameActionAgent
from backend.agent.action_parser import ActionParser
from backend.blender.fbx_exporter import FBXExporter
from backend.utils.file import load_json_file


# 初始化 FastAPI 应用
app = FastAPI(
    title="Motion Agent API",
    description="将自然语言转换为游戏动作命令的 AI Agent API",
    version="1.0.0"
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],  # 前端地址
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
game_agent: Optional[GameActionAgent] = None
action_parser: Optional[ActionParser] = None
fbx_exporter: Optional[FBXExporter] = None


# Pydantic 模型
class ParseRequest(BaseModel):
    """解析请求模型"""
    text: str = Field(..., description="要解析的自然语言文本", min_length=1, max_length=1000)
    use_llm: bool = Field(True, description="是否使用 LLM 进行解析")
    model_name: str = Field("gpt-4o-mini", description="使用的模型名称")
    temperature: float = Field(0.1, description="模型温度参数", ge=0.0, le=2.0)


class ParseResponse(BaseModel):
    """解析响应模型"""
    success: bool = Field(..., description="Whether parsing was successful")
    actions: List[Dict[str, Any]] = Field(..., description="List of parsed action commands")
    error: Optional[str] = Field(None, description="Error message if any")
    input_text: str = Field(..., description="Original input text")
    parsing_method: str = Field(..., description="Parsing method used")


class ValidationRequest(BaseModel):
    """验证请求模型"""
    actions: List[Dict[str, Any]] = Field(..., description="要验证的动作命令列表")


class ValidationResponse(BaseModel):
    """验证响应模型"""
    is_valid: bool = Field(..., description="Whether the actions are valid")
    error_messages: List[str] = Field(..., description="List of error messages")
    warning_messages: List[str] = Field(..., description="List of warning messages")
    total_actions: int = Field(..., description="Total number of actions")


class FBXExportRequest(BaseModel):
    """FBX 导出请求模型"""
    actions: List[Dict[str, Any]] = Field(..., description="要导出的动作命令列表")
    project_name: str = Field("MotionAgentAnimation", description="项目名称")
    character_name: str = Field("Character", description="角色名称")
    output_filename: Optional[str] = Field(None, description="输出文件名（不含路径）")


class FBXExportResponse(BaseModel):
    """FBX 导出响应模型"""
    export_success: bool = Field(..., description="Whether export was successful")
    output_file_path: Optional[str] = Field(None, description="Output file path")
    file_size_bytes: Optional[int] = Field(None, description="File size in bytes")
    total_actions: int = Field(..., description="Number of actions processed")
    project_name: str = Field(..., description="Project name")
    error_message: Optional[str] = Field(None, description="Error message if any")
    download_url: Optional[str] = Field(None, description="Download URL")


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global game_agent, action_parser, fbx_exporter

    logger.info("正在启动 Motion Agent API...")

    # 检查环境变量
    if not os.getenv("OPENAI_API_KEY"):
        logger.warning("OPENAI_API_KEY 环境变量未设置，LLM 功能可能无法使用")

    try:
        # 初始化组件
        action_parser = ActionParser()
        logger.info("动作解析器初始化完成")

        # 初始化 FBX 导出器
        fbx_exporter = FBXExporter()
        blender_info = fbx_exporter.get_blender_info()
        if blender_info["available"]:
            logger.info(f"Blender 可用: {blender_info['version']}")
        else:
            logger.warning(f"Blender 不可用: {blender_info.get('error', 'Unknown error')}")

        # 只有在有 API Key 时才初始化 LLM Agent
        if os.getenv("OPENAI_API_KEY"):
            game_agent = GameActionAgent()
            logger.info("游戏动作 Agent 初始化完成")
        else:
            logger.warning("跳过 LLM Agent 初始化（缺少 API Key）")

        logger.info("Motion Agent API 启动完成")

    except Exception as e:
        logger.error(f"启动失败: {e}")
        raise


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "Motion Agent API",
        "version": "1.0.0",
        "status": "running",
        "llm_available": game_agent is not None
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    blender_info = fbx_exporter.get_blender_info() if fbx_exporter else {"available": False}

    return {
        "status": "healthy",
        "components": {
            "action_parser": action_parser is not None,
            "game_agent": game_agent is not None,
            "fbx_exporter": fbx_exporter is not None,
            "blender": blender_info["available"]
        },
        "blender_info": blender_info
    }


@app.get("/actions")
async def get_supported_actions():
    """获取支持的动作列表"""
    try:
        actions = load_json_file("backend/actions/game_action_command_set.json")
        return {
            "success": True,
            "actions": actions,
            "count": len(actions)
        }
    except Exception as e:
        logger.error(f"获取动作列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取动作列表失败: {str(e)}")


@app.post("/parse", response_model=ParseResponse)
async def parse_natural_language(request: ParseRequest):
    """解析自然语言为游戏动作命令"""
    try:
        logger.info(f"收到解析请求: {request.text[:50]}...")

        if request.use_llm and game_agent is not None:
            # 使用 LLM 解析
            logger.info("使用 LLM 进行解析")

            # 如果需要使用不同的模型参数，重新初始化 agent
            if (request.model_name != "gpt-4o-mini" or
                abs(request.temperature - 0.1) > 0.01):
                temp_agent = GameActionAgent(
                    model_name=request.model_name,
                    temperature=request.temperature
                )
                result = await temp_agent.aparse_natural_language(request.text)
            else:
                result = await game_agent.aparse_natural_language(request.text)

            result["parsing_method"] = "llm"
            result["input_text"] = result.pop("input", request.text)

        else:
            # 使用规则解析
            logger.info("使用规则解析器进行解析")

            if action_parser is None:
                raise HTTPException(status_code=500, detail="动作解析器未初始化")

            actions = action_parser.parse_simple_command(request.text)
            result = {
                "success": True,
                "actions": actions,
                "error": None,
                "input_text": request.text,
                "parsing_method": "rule_based"
            }

        logger.info(f"解析完成，生成了 {len(result['actions'])} 个动作")
        return ParseResponse(**result)

    except Exception as e:
        logger.error(f"解析失败: {e}")
        raise HTTPException(status_code=500, detail=f"解析失败: {str(e)}")


@app.post("/validate", response_model=ValidationResponse)
async def validate_actions(request: ValidationRequest):
    """验证动作命令序列"""
    try:
        logger.info(f"收到验证请求，包含 {len(request.actions)} 个动作")

        if action_parser is None:
            raise HTTPException(status_code=500, detail="动作解析器未初始化")

        result = action_parser.validate_command_sequence(request.actions)

        logger.info(f"验证完成，结果: {'有效' if result['valid'] else '无效'}")
        return ValidationResponse(**result)

    except Exception as e:
        logger.error(f"验证失败: {e}")
        raise HTTPException(status_code=500, detail=f"验证失败: {str(e)}")


@app.post("/export-fbx", response_model=FBXExportResponse)
async def export_fbx(request: FBXExportRequest):
    """导出动作为 FBX 文件"""
    try:
        logger.info(f"收到 FBX 导出请求: {request.project_name}, {len(request.actions)} 个动作")

        if fbx_exporter is None:
            raise HTTPException(status_code=500, detail="FBX 导出器未初始化")

        # 验证动作
        validation = fbx_exporter.validate_actions_for_export(request.actions)
        if not validation["valid"]:
            raise HTTPException(
                status_code=400,
                detail=f"动作验证失败: {', '.join(validation['errors'])}"
            )

        # 生成输出文件名
        if request.output_filename:
            filename = request.output_filename
            if not filename.endswith('.fbx'):
                filename += '.fbx'
        else:
            filename = f"{request.project_name}.fbx"

        # 创建输出目录
        output_dir = os.path.join(os.getcwd(), "exports")
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, filename)

        # 导出 FBX
        result = fbx_exporter.export_actions_to_fbx(
            actions=request.actions,
            output_path=output_path,
            project_name=request.project_name,
            character_name=request.character_name
        )

        if result["success"]:
            # 生成下载链接
            download_url = f"/download/{filename}"

            logger.info(f"FBX 导出成功: {output_path}")

            return FBXExportResponse(
                success=True,
                output_path=output_path,
                file_size=result.get("file_size"),
                actions_count=len(request.actions),
                project_name=request.project_name,
                download_url=download_url
            )
        else:
            raise HTTPException(
                status_code=500,
                detail=f"FBX 导出失败: {result.get('error', 'Unknown error')}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"FBX 导出失败: {e}")
        raise HTTPException(status_code=500, detail=f"FBX 导出失败: {str(e)}")


@app.get("/download/{filename}")
async def download_fbx(filename: str):
    """下载 FBX 文件"""
    try:
        # 安全检查文件名
        if not filename.endswith('.fbx') or '/' in filename or '\\' in filename:
            raise HTTPException(status_code=400, detail="无效的文件名")

        file_path = os.path.join(os.getcwd(), "exports", filename)

        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="文件不存在")

        from fastapi.responses import FileResponse
        return FileResponse(
            path=file_path,
            filename=filename,
            media_type='application/octet-stream'
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件下载失败: {e}")
        raise HTTPException(status_code=500, detail=f"文件下载失败: {str(e)}")


@app.get("/blender-info")
async def get_blender_info():
    """获取 Blender 信息"""
    try:
        if fbx_exporter is None:
            return {"available": False, "error": "FBX 导出器未初始化"}

        return fbx_exporter.get_blender_info()

    except Exception as e:
        logger.error(f"获取 Blender 信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取 Blender 信息失败: {str(e)}")


if __name__ == "__main__":
    # 开发模式运行
    uvicorn.run(
        "backend.app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )