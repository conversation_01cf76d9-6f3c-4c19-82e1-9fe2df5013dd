[
  {
    name: "move",
    key: "MOVE",
    desc: "基础移动 / Basic Movement",
    params: {
      direction: "forward | backward | left | right",
      speed: "float (0.0 - 1.0)"
    }
  },
  {
    name: "jump",
    key: "JUMP",
    desc: "跳跃 / Jump",
    params: {
      height: "float",
      distance: "float"
    }
  },
  {
    name: "attack",
    key: "ATTACK",
    desc: "攻击动作 / Attack",
    params: {
      weapon: "sword | gun | magic | fist",
      style: "slash | thrust | shoot | cast",
      strength: "float (0.0 - 1.0)"
    }
  },
  {
    name: "defend",
    key: "DEFEND",
    desc: "防御动作 / Defend",
    params: {
      type: "block | dodge | parry",
      direction: "front | left | right | back"
    }
  },
  {
    name: "idle",
    key: "IDLE",
    desc: "待机状态 / Idle State",
    params: {
      mood: "relaxed | alert | injured"
    }
  },
  {
    name: "state_transition",
    key: "STATE",
    desc: "角色状态转换 / State Transition",
    params: {
      from: "idle | combat | walk | run",
      to: "idle | combat | walk | run"
    }
  },
  {
    name: "ui_animation",
    key: "UI",
    desc: "UI 动画效果 / UI Animation",
    params: {
      type: "fade_in | fade_out | bounce | scale",
      duration: "float (seconds)"
    }
  },
  {
    name: "emotion",
    key: "EMOTION",
    desc: "情绪表达 / Emotion",
    params: {
      emotion: "happy | angry | sad | surprised",
      intensity: "float (0.0 - 1.0)"
    }
  },
  {
    name: "interaction",
    key: "INTERACT",
    desc: "环境互动 / Environment Interaction",
    params: {
      object: "door | lever | item | npc",
      action: "open | pull | pick_up | talk"
    }
  },
  {
    name: "contextual_action",
    key: "CONTEXT",
    desc: "情景动作 / Contextual Action",
    params: {
      context: "ledge | vault | crawl_space",
      action: "climb | vault | crawl"
    }
  },
  {
    name: "combo",
    key: "COMBO",
    desc: "连击动作 / Combo",
    params: {
      sequence: ["ATTACK", "ATTACK", "DEFEND"],
      timing: "array of float"
    }
  },
  {
    name: "roll",
    key: "ROLL",
    desc: "翻滚 / Dodge Roll",
    params: {
      direction: "left | right | back",
      distance: "float"
    }
  },
  {
    name: "sneak",
    key: "SNEAK",
    desc: "潜行 / Sneak",
    params: {
      speed: "float (0.0 - 0.5)",
      crouch: "boolean"
    }
  },
  {
    name: "drive",
    key: "DRIVE",
    desc: "驾驶 / Drive",
    params: {
      vehicle: "car | bike | tank",
      speed: "float",
      mode: "normal | boost"
    }
  },
  {
    name: "team_action",
    key: "TEAM",
    desc: "组队动作 / Team Coordination",
    params: {
      role: "leader | follower",
      action: "signal | assist | sync_move"
    }
  },
  {
    name: "cinematic",
    key: "CINEMA",
    desc: "演出过场 / Cinematic",
    params: {
      camera_motion: "pan | zoom | rotate",
      actor_action: "pose | speak | react"
    }
  }
]

