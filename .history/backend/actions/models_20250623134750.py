# game_action_parser/models.py
import json
from pydantic import BaseModel, Field, conlist, confloat
from typing import List, Literal, Optional, Union, Dict, Any
import os

# 获取当前文件所在目录，然后向上移动到项目根目录，再进入 config
_current_dir = os.path.dirname(os.path.abspath(__file__))
_config_path = os.path.join(_current_dir, 'game_action_command_set.json')

try:
    with open(_config_path, 'r', encoding='utf-8') as f:
        ACTION_COMMAND_SET = json.load(f)
except FileNotFoundError:
    raise FileNotFoundError(f"无法找到动作指令集文件: {_config_path}")
except json.JSONDecodeError:
    raise ValueError(f"动作指令集文件格式错误: {_config_path}")

# 存储动态生成的 Pydantic 类
DYNAMIC_ACTION_MODELS = {}
ACTION_NAME_TO_KEY_MAP = {}

for action_def in ACTION_COMMAND_SET:
    action_name = action_def["name"]
    action_key = action_def["key"]
    ACTION_NAME_TO_KEY_MAP[action_name] = action_key
    
    params = {}
    for param_name, param_type_str in action_def["params"].items():
        if isinstance(param_type_str, list):
            if param_name == "sequence":
                params[param_name] = conlist(str, min_length=1)
            elif param_name == "timing":
                params[param_name] = conlist(confloat(ge=0.0), min_length=1)
            else:
                params[param_name] = List[Any]
        elif "|" in param_type_str:
            enum_values = [Literal[val.strip()] for val in param_type_str.split('|')]
            params[param_name] = Union[*enum_values]
        elif "float" in param_type_str:
            if "(" in param_type_str:
                try:
                    parts = param_type_str.split('(')[1].split(')')[0].split('-')
                    min_val = float(parts[0].strip())
                    max_val = float(parts[1].strip())
                    params[param_name] = confloat(ge=min_val, le=max_val)
                except:
                    params[param_name] = float
            else:
                params[param_name] = float
        elif "int" in param_type_str:
            params[param_name] = int
        elif "boolean" in param_type_str:
            params[param_name] = bool
        elif "string" in param_type_str:
            params[param_name] = str
        else:
            params[param_name] = str # Fallback

    optional_params = {k: Optional[v] for k, v in params.items()}
    class_name = f"Action_{action_key.replace('-', '_')}"
    
    ActionModel = type(class_name, (BaseModel,), {
        **optional_params,
        'model_config': {'extra': 'allow'}
    })
    
    DYNAMIC_ACTION_MODELS[action_name] = ActionModel

# 定义一个主要的动作执行器，它将作为 LLM 的主要工具
class ExecuteAction(BaseModel):
    """根据用户指令执行一个或多个游戏动作。"""
    actions: List[Dict[str, Any]] = Field(
        ...,
        description=f"要执行的动作列表，每个动作包含 'type' (动作名称) 和 'params' (动作参数)。"
                    f"动作类型必须是 {list(DYNAMIC_ACTION_MODELS.keys())} 中的一个。"
                    f"参数根据动作类型而定，必须符合每个动作类型定义的参数结构。"
                    f"例如：[{{'type': 'move', 'params': {{'direction': 'forward', 'speed': 0.8}}}}, {{'type': 'jump', 'params': {{'height': 2.0}}}}]"
    )

    def validate_actions_list(self) -> List[Dict[str, Any]]:
        """
        验证并返回经过类型检查和清理的动作列表。
        移除未设置的参数，确保输出干净。
        """
        validated_actions = []
        for action_data in self.actions:
            action_type = action_data.get("type")
            params = action_data.get("params", {})
            if action_type not in DYNAMIC_ACTION_MODELS:
                raise ValueError(f"未知动作类型: '{action_type}'。可用类型: {list(DYNAMIC_ACTION_MODELS.keys())}")
            
            ActionClass = DYNAMIC_ACTION_MODELS[action_type]
            
            try:
                # 尝试验证参数
                validated_params = ActionClass(**params)
                # 使用 model_dump(exclude_unset=True) 来清理未由 LLM 设置的参数
                validated_actions.append({
                    "type": action_type,
                    "params": validated_params.model_dump(exclude_unset=True) 
                })
            except Exception as e:
                raise ValueError(f"动作 '{action_type}' 的参数验证失败: {e}，原始参数: {params}")
        return validated_actions