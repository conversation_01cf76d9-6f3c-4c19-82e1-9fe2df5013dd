import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  webpack: (config) => {
    config.module.rules.push({
      test: /\.(fbx|glb|gltf)$/,
      type: 'asset/resource',
      generator: {
        filename: 'static/models/[name][ext]'
      }
    });
    return config;
  },
  // Configure static file serving
  async rewrites() {
    return [
      {
        source: '/static/models/:path*',
        destination: '/_next/static/models/:path*',
      },
    ];
  }
};

export default nextConfig;
