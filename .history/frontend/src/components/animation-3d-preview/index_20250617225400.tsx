'use client';

import { Suspense } from 'react';
import { Canvas } from '@react-three/fiber';
import Scene3D from './Scene3D';

interface Animation3DPreviewProps {
  fbxUrl?: string;
  animationName?: string;
}

export default function Animation3DPreview({
  fbxUrl,
  animationName
}: Animation3DPreviewProps) {

  return (
    <div className="w-full h-full">
      {fbxUrl && (
        <Canvas
          camera={{ position: [5, 5, 5], fov: 50 }}
          shadows
          className="w-full h-full"
        >
          <Suspense fallback={null}>
            <Scene3D
              fbxUrl={fbxUrl}
            />
          </Suspense>
        </Canvas>
      )}
    </div>
  );
}
