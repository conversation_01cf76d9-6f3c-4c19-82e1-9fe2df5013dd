import { useLoader } from '@react-three/fiber';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader';

interface FBXModelProps {
  url: string;
}

export default function FBXModel({ 
  url, 
  isPlaying, 
  progress, 
  onLoadComplete, 
  onLoadError 
}: FBXModelProps) {
  const groupRef = useRef<THREE.Group>(null);
  const mixerRef = useRef<THREE.AnimationMixer | null>(null);
  const actionsRef = useRef<THREE.AnimationAction[]>([]);
  const clockRef = useRef(new THREE.Clock());
  const [model, setModel] = useState<THREE.Group | null>(null);

  // Load FBX model
  useEffect(() => {
    if (!url) return;

    const loader = new FBXLoader();
    
    loader.load(
      url,
      (fbx) => {
        try {
          // Scale the model appropriately
          fbx.scale.setScalar(0.01); // Adjust scale as needed
          
          // Center the model
          const box = new THREE.Box3().setFromObject(fbx);
          const center = box.getCenter(new THREE.Vector3());
          fbx.position.sub(center);
          
          // Set up animation mixer
          if (fbx.animations && fbx.animations.length > 0) {
            const mixer = new THREE.AnimationMixer(fbx);
            mixerRef.current = mixer;
            
            // Create actions for all animations
            const actions = fbx.animations.map(clip => {
              const action = mixer.clipAction(clip);
              action.setLoop(THREE.LoopRepeat, Infinity);
              return action;
            });
            
            actionsRef.current = actions;
            
            // Get duration from first animation
            const duration = fbx.animations[0].duration;
            onLoadComplete(duration);
            
            // Start the first animation
            if (actions.length > 0) {
              actions[0].play();
            }
          } else {
            onLoadComplete(0);
          }
          
          setModel(fbx);
        } catch (error) {
          onLoadError(error instanceof Error ? error.message : 'Failed to process FBX model');
        }
      },
      (progress) => {
        // Loading progress
        console.log('Loading progress:', (progress.loaded / progress.total) * 100 + '%');
      },
      (error) => {
        onLoadError(error instanceof Error ? error.message : 'Failed to load FBX model');
      }
    );

    return () => {
      // Cleanup
      if (mixerRef.current) {
        mixerRef.current.stopAllAction();
        mixerRef.current = null;
      }
      actionsRef.current = [];
    };
  }, [url, onLoadComplete, onLoadError]);

  // Handle play/pause
  useEffect(() => {
    if (!mixerRef.current || actionsRef.current.length === 0) return;

    actionsRef.current.forEach(action => {
      if (isPlaying) {
        action.paused = false;
      } else {
        action.paused = true;
      }
    });
  }, [isPlaying]);

  // Handle progress seeking
  useEffect(() => {
    if (!mixerRef.current || actionsRef.current.length === 0) return;

    const action = actionsRef.current[0];
    if (action && action.getClip()) {
      const duration = action.getClip().duration;
      const time = (progress / 100) * duration;
      mixerRef.current.setTime(time);
    }
  }, [progress]);

  // Animation loop
  useFrame(() => {
    if (mixerRef.current && isPlaying) {
      const delta = clockRef.current.getDelta();
      mixerRef.current.update(delta);
    }
  });

  return (
    <group ref={groupRef}>
      {model && <primitive object={model} />}
    </group>
  );
}
