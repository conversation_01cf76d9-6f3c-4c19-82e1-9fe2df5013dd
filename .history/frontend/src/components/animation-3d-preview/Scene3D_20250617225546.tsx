import { OrbitControls } from '@react-three/drei';
import FBXModel from './FBXModel';

interface Scene3DProps {
  fbxUrl: string;
}

export default function Scene3D({ fbxUrl }: Scene3DProps) {
  return (
    <>
      <OrbitControls
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        minDistance={1}
        maxDistance={50}
        target={[0, 1, 0]}
      />
      <ambientLight intensity={0.5} />
      <directionalLight position={[10, 10, 5]} intensity={1} />
      <directionalLight position={[-10, 10, -5]} intensity={0.5} />
      <FBXModel url={fbxUrl} />
    </>
  );
}
