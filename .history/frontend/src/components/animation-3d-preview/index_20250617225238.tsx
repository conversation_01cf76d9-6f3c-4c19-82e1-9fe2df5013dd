'use client';

import { Suspense } from 'react';
import { Canvas } from '@react-three/fiber';
import Scene3D from './Scene3D';

interface Animation3DPreviewProps {
  fbxUrl?: string;
  animationName?: string;
}

export default function Animation3DPreview({
  fbxUrl,
  animationName,
  isOpen,
  onClose,
  isPlaying: propsIsPlaying
}: Animation3DPreviewProps) {
  const [isPlaying, setIsPlaying] = useState(!!propsIsPlaying);
  const [isMuted, setIsMuted] = useState(true);
  const [progress, setProgress] = useState(0);
  const [duration, setDuration] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showStats, setShowStats] = useState(false);

  useEffect(() => {
    if (!isOpen || !fbxUrl) {
      setLoading(true);
      setError(null);
      return;
    }

    // Reset state when opening
    setLoading(true);
    setError(null);
    setIsPlaying(false);
    setProgress(0);
    setDuration(0);
  }, [isOpen, fbxUrl]);

  const handleLoadComplete = (animationDuration: number) => {
    setDuration(animationDuration);
    setLoading(false);
    setError(null);
  };

  const handleLoadError = (errorMessage: string) => {
    setError(errorMessage);
    setLoading(false);
  };

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const handleRestart = () => {
    setProgress(0);
    setIsPlaying(true);
  };

  const handleProgressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newProgress = parseFloat(e.target.value);
    setProgress(newProgress);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-4xl h-full max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div>
            <h3 className="text-lg font-semibold">3D Animation Preview</h3>
            {animationName && (
              <p className="text-sm text-gray-600">{animationName}</p>
            )}
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* 3D Viewport */}
        <div className="flex-1 relative bg-gray-900">
          {loading ? (
            <div className="absolute inset-0 flex items-center justify-center z-10">
              <div className="text-center text-white">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
                <p>Loading 3D animation...</p>
              </div>
            </div>
          ) : error ? (
            <div className="absolute inset-0 flex items-center justify-center z-10">
              <div className="text-center text-white max-w-md">
                <AlertCircle className="w-12 h-12 text-yellow-400 mx-auto mb-4" />
                <h4 className="text-lg font-medium mb-2">Preview Not Available</h4>
                <p className="text-gray-300 text-sm">{error}</p>
                <div className="mt-4 p-4 bg-gray-800 rounded-lg text-left">
                  <h5 className="font-medium mb-2">Recommended 3D Software:</h5>
                  <ul className="text-sm space-y-1">
                    <li>• Autodesk Maya</li>
                    <li>• 3ds Max</li>
                    <li>• Blender (Free)</li>
                    <li>• Cinema 4D</li>
                    <li>• Unity 3D</li>
                  </ul>
                </div>
              </div>
            </div>
          ) : null}

          {/* Three.js Canvas */}
          {fbxUrl && (
            <Canvas
              camera={{ position: [5, 5, 5], fov: 50 }}
              shadows
              className="w-full h-full"
            >
              <Suspense fallback={null}>
                <Scene3D
                  fbxUrl={fbxUrl}
                  isPlaying={isPlaying}
                  progress={progress}
                  showStats={showStats}
                  onLoadComplete={handleLoadComplete}
                  onLoadError={handleLoadError}
                />
              </Suspense>
            </Canvas>
          )}

          {/* 3D Controls Overlay */}
          {!loading && !error && (
            <div className="absolute top-4 right-4 bg-black bg-opacity-50 rounded-lg p-2 z-10">
              <div className="text-white text-xs space-y-1">
                <div>Mouse: Rotate view</div>
                <div>Wheel: Zoom</div>
                <div>Right-click: Pan</div>
                <button
                  onClick={() => setShowStats(!showStats)}
                  className="mt-2 text-xs bg-blue-600 px-2 py-1 rounded hover:bg-blue-700"
                >
                  {showStats ? 'Hide Stats' : 'Show Stats'}
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Animation Controls */}
        <div className="p-4 border-t bg-gray-50">
          <div className="flex items-center space-x-4">
            {/* Play/Pause */}
            <button
              onClick={handlePlayPause}
              disabled={loading || !!error}
              className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
            </button>

            {/* Restart */}
            <button
              onClick={handleRestart}
              disabled={loading || !!error}
              className="p-2 text-gray-600 hover:bg-gray-200 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <RotateCcw className="w-5 h-5" />
            </button>

            {/* Progress Bar */}
            <div className="flex-1">
              <input
                type="range"
                min="0"
                max="100"
                value={progress}
                onChange={handleProgressChange}
                disabled={loading || !!error}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer disabled:opacity-50"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>{formatTime(progress * duration / 100)}</span>
                <span>{formatTime(duration)}</span>
              </div>
            </div>

            {/* Volume */}
            <button
              onClick={() => setIsMuted(!isMuted)}
              disabled={loading || !!error}
              className="p-2 text-gray-600 hover:bg-gray-200 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
            </button>

            {/* Fullscreen */}
            <button
              onClick={() => {
                // Toggle fullscreen
                if (document.fullscreenElement) {
                  document.exitFullscreen();
                } else {
                  document.documentElement.requestFullscreen();
                }
              }}
              disabled={loading || !!error}
              className="p-2 text-gray-600 hover:bg-gray-200 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Maximize className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
