'use client';

import React, { useRef, useEffect, useState } from 'react';
import { Canvas, useFrame, useLoader } from '@react-three/fiber';
import { OrbitControls, Environment, Grid, Stats } from '@react-three/drei';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader';
import * as THREE from 'three';

interface AnimationPreviewProps {
  fbxUrl: string;
  animationName?: string;
  autoPlay?: boolean;
  showGrid?: boolean;
  showStats?: boolean;
  cameraPosition?: [number, number, number];
  backgroundColor?: string;
}

function FBXModel({ 
  fbxUrl, 
  autoPlay = true 
}: { 
  fbxUrl: string; 
  autoPlay?: boolean; 
}) {
  const groupRef = useRef<THREE.Group>(null);
  const mixerRef = useRef<THREE.AnimationMixer | null>(null);
  const [model, setModel] = useState<THREE.Group | null>(null);
  const [animations, setAnimations] = useState<THREE.AnimationClip[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load FBX model
  useEffect(() => {
    console.log('Starting to load FBX from:', fbxUrl);
    const loader = new FBXLoader();

    loader.load(
      fbxUrl,
      (fbx) => {
        try {
          console.log('FBX loaded successfully:', fbx);
          console.log('Animations found:', fbx.animations.length);

          // Scale the model appropriately
          fbx.scale.setScalar(0.01);

          // Center the model
          const box = new THREE.Box3().setFromObject(fbx);
          const center = box.getCenter(new THREE.Vector3());
          fbx.position.sub(center);

          // Set up animations
          if (fbx.animations && fbx.animations.length > 0) {
            const mixer = new THREE.AnimationMixer(fbx);
            mixerRef.current = mixer;

            // Play the first animation by default
            if (autoPlay) {
              const action = mixer.clipAction(fbx.animations[0]);
              action.play();
              console.log('Animation started playing');
            }

            setAnimations(fbx.animations);
          } else {
            console.log('No animations found in FBX file');
          }

          setModel(fbx);
          setIsLoading(false);
        } catch (err) {
          console.error('Error processing FBX model:', err);
          setError('Failed to process FBX model');
          setIsLoading(false);
        }
      },
      (progress) => {
        console.log('Loading progress:', (progress.loaded / progress.total) * 100 + '%');
      },
      (error) => {
        console.error('Error loading FBX:', error);
        setError('Failed to load FBX file: ' + error.message);
        setIsLoading(false);
      }
    );

    return () => {
      if (mixerRef.current) {
        mixerRef.current.stopAllAction();
      }
    };
  }, [fbxUrl, autoPlay]);

  // Animation loop
  useFrame((state, delta) => {
    if (mixerRef.current) {
      mixerRef.current.update(delta);
    }
  });

  if (isLoading) {
    return (
      <mesh>
        <boxGeometry args={[1, 1, 1]} />
        <meshStandardMaterial color="gray" wireframe />
      </mesh>
    );
  }

  if (error || !model) {
    return (
      <mesh>
        <boxGeometry args={[1, 1, 1]} />
        <meshStandardMaterial color="red" />
      </mesh>
    );
  }

  return (
    <group ref={groupRef}>
      <primitive object={model} />
    </group>
  );
}

export default function AnimationPreview({
  fbxUrl,
  animationName = "Animation",
  autoPlay = true,
  showGrid = true,
  showStats = false,
  cameraPosition = [5, 5, 5],
  backgroundColor = "#f0f0f0"
}: AnimationPreviewProps) {
  return (
    <div className="w-full h-96 border border-gray-300 rounded-lg overflow-hidden">
      <div className="bg-gray-100 px-4 py-2 border-b border-gray-300">
        <h3 className="text-lg font-semibold text-gray-800">{animationName}</h3>
      </div>
      
      <div className="w-full h-full relative" style={{ height: 'calc(100% - 48px)' }}>
        <Canvas
          camera={{ 
            position: cameraPosition, 
            fov: 50,
            near: 0.1,
            far: 1000
          }}
          style={{ background: backgroundColor }}
        >
          {/* Lighting */}
          <ambientLight intensity={0.6} />
          <directionalLight 
            position={[10, 10, 5]} 
            intensity={1}
            castShadow
            shadow-mapSize-width={2048}
            shadow-mapSize-height={2048}
          />
          <pointLight position={[-10, -10, -10]} intensity={0.3} />
          
          {/* Environment */}
          <Environment preset="studio" />
          
          {/* Grid */}
          {showGrid && (
            <Grid 
              args={[10, 10]} 
              cellSize={1} 
              cellThickness={0.5} 
              cellColor="#6e6e6e" 
              sectionSize={5} 
              sectionThickness={1} 
              sectionColor="#9d4b4b" 
              fadeDistance={25} 
              fadeStrength={1} 
              followCamera={false} 
              infiniteGrid={true}
            />
          )}
          
          {/* FBX Model */}
          <FBXModel fbxUrl={fbxUrl} autoPlay={autoPlay} />
          
          {/* Controls */}
          <OrbitControls 
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            minDistance={1}
            maxDistance={50}
          />
          
          {/* Stats */}
          {showStats && <Stats />}
        </Canvas>
        
        {/* Loading overlay */}
        <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm">
          FBX Animation Preview
        </div>
      </div>
    </div>
  );
}
