'use client';

import { useState } from 'react';
import Animation3DPreview from './animation-3d-preview';

interface AnimationPreviewWrapperProps {
  fbxUrl: string;
  animationName: string;
}

export default function AnimationPreviewWrapper({ fbxUrl, animationName }: AnimationPreviewWrapperProps) {
  const [isOpen, setIsOpen] = useState(true);

  return (
    <Animation3DPreview
      fbxUrl={fbxUrl}
      animationName={animationName}
      isOpen={isOpen}
      onClose={() => setIsOpen(false)}
    />
  );
}
