'use client';

import AnimationPreview from '@/components/animation-preview';

export default function Home() {
  return (
    <div className="min-h-screen p-8 bg-gray-50">
      <div className="max-w-6xl mx-auto">
        <header className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            FBX Animation Preview
          </h1>
          <p className="text-gray-600">
            Preview and interact with 3D FBX animations using React Three Fiber
          </p>
        </header>

        <main className="space-y-8">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-800">
              Samba Dancing Animation
            </h2>
            <AnimationPreview
              fbxUrl="/models/SambaDancing.fbx"
              animationName="Samba Dance"
              autoPlay={true}
              showGrid={true}
              showStats={false}
              cameraPosition={[5, 5, 5]}
              backgroundColor="#f8fafc"
            />
          </div>

          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold mb-3 text-gray-800">
              Controls
            </h3>
            <ul className="text-gray-600 space-y-1">
              <li>• Left click + drag: Rotate camera</li>
              <li>• Right click + drag: Pan camera</li>
              <li>• Scroll wheel: Zoom in/out</li>
              <li>• Animation plays automatically</li>
            </ul>
          </div>
        </main>
      </div>
    </div>
  );
}
