'use client';

import Image from "next/image";
import AnimationPreviewWrapper from '@/components/animation-preview-wrapper';

// Use dynamic import for FBX file
const fbxPath = '/static/models/SambaDancing.fbx'; 

export default function Home() {
  return (
    <div className="grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20 font-[family-name:var(--font-geist-sans)]">
      <main className="flex flex-col gap-[32px] row-start-2 items-center sm:items-start">
        <AnimationPreviewWrapper
          fbxUrl={fbxPath}
          animationName="Samba Dance"
        />
      </main>
    </div>
  );
}
