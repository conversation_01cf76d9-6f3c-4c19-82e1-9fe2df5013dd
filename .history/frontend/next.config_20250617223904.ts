import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  webpack: (config) => {
    config.module.rules.push({
      test: /\.(fbx|glb|gltf)$/,
      use: {
        loader: 'file-loader',
        options: {
          name: '[name].[ext]',
          outputPath: 'static/models/',
          publicPath: '_next/static/models/'
        }
      }
    });
    return config;
  }
};

export default nextConfig;
