# Motion Agent - 自然语言到 FBX 动画转换系统

## 🎯 项目概述

Motion Agent 是一个完整的系统，能够将自然语言描述转换为 3D 动画，并导出为 FBX 文件格式。系统使用 LangChain 和 LangGraph 构建智能 Agent，支持从自然语言到 Blender 动画数据的完整转换流程。

## ✅ 已实现功能

### 1. 自然语言解析 Agent
- **LangGraph Agent** (`backend/agent/animation_agent.py`)
  - 使用 LangChain 和 LangGraph 构建的状态图工作流
  - 支持 OpenAI GPT 模型进行智能解析
  - 包含解析、验证和错误处理节点

- **规则解析器** (`backend/agent/action_parser.py`)
  - 基于规则的备选解析方案
  - 时间、移动、情绪、动作的提取功能
  - 不依赖外部 API，可离线使用

### 2. 游戏动作命令系统
- **动作定义** (`backend/actions/game_action_command_set.json`)
  - 27 种预定义的游戏动作类型
  - 包括移动、跳跃、攻击、防御、情绪等
  - 每个动作都有详细的参数定义

### 3. Blender 集成系统
- **动画数据格式** (`backend/blender/animation_schema.py`)
  - 定义了完整的 Blender 动画数据结构
  - 支持关键帧、插值、骨骼动画等
  - 包含场景、导出设置等配置

- **格式转换器** (`backend/blender/converter.py`)
  - 将游戏动作命令转换为 Blender 动画数据
  - 支持位置、旋转、缩放动画
  - 处理时间序列和动画通道

- **Blender 脚本** (`backend/blender/blender_script.py`)
  - 在 Blender 中执行的 Python 脚本
  - 创建角色、骨架和动画
  - 自动导出 FBX 文件

- **FBX 导出器** (`backend/blender/fbx_exporter.py`)
  - 管理整个 Blender 调用流程
  - 处理临时文件和错误处理
  - 验证导出结果

### 4. REST API 服务
- **FastAPI 应用** (`backend/app.py`)
  - `/parse` - 解析自然语言为动作命令
  - `/validate` - 验证动作命令序列
  - `/export-fbx` - 导出 FBX 文件
  - `/download/{filename}` - 下载生成的文件
  - `/health` - 健康检查
  - `/blender-info` - Blender 环境信息

### 5. 前端 3D 预览
- **React Three Fiber 组件** (`frontend/src/components/animation-preview.tsx`)
  - 使用 @react-three/fiber 和 @react-three/drei
  - 支持 FBX 文件加载和预览
  - 交互式 3D 场景控制

## 🔄 完整工作流程

### 示例输入
```
"人物挥手3秒，然后向前五步走，并跳跃"
```

### 处理流程

1. **自然语言解析**
   ```json
   [
     {
       "action": "emotion",
       "key": "EMOTION",
       "params": {"emotion": "happy", "intensity": 0.7},
       "sequence": 1,
       "description": "挥手表达情绪"
     },
     {
       "action": "idle",
       "key": "IDLE", 
       "params": {"mood": "alert", "duration": 3.0},
       "sequence": 2,
       "description": "挥手动作持续3秒"
     },
     {
       "action": "move",
       "key": "MOVE",
       "params": {"direction": "forward", "speed": 0.5, "duration": 5.0},
       "sequence": 3,
       "description": "向前走五步"
     },
     {
       "action": "jump",
       "key": "JUMP",
       "params": {"height": 1.0, "distance": 1.0, "style": "normal"},
       "sequence": 4,
       "description": "跳跃动作"
     }
   ]
   ```

2. **转换为 Blender 格式**
   - 创建动画通道和关键帧
   - 设置时间轴和帧率
   - 生成完整的 Blender 项目数据

3. **Blender 处理**
   - 创建角色和骨架
   - 应用动画数据
   - 导出 FBX 文件

4. **结果输出**
   - 生成可用的 FBX 动画文件
   - 提供下载链接
   - 支持前端预览

## 🚀 使用方法

### 1. 启动 API 服务器
```bash
source .venv/bin/activate
python -m uvicorn backend.app:app --host 0.0.0.0 --port 8000 --reload
```

### 2. 启动前端服务器
```bash
cd frontend
npm run dev
```

### 3. API 调用示例

#### 解析自然语言
```bash
curl -X POST "http://localhost:8000/parse" \
  -H "Content-Type: application/json" \
  -d '{"text": "人物挥手3秒，然后向前五步走，并跳跃", "use_llm": false}'
```

#### 导出 FBX
```bash
curl -X POST "http://localhost:8000/export-fbx" \
  -H "Content-Type: application/json" \
  -d '{
    "actions": [...],
    "project_name": "MyAnimation",
    "character_name": "Character"
  }'
```

### 4. 程序化使用
```python
from backend.agent.animation_agent import GameActionAgent
from backend.blender.fbx_exporter import FBXExporter

# 使用 LLM Agent
agent = GameActionAgent()
result = await agent.aparse_natural_language("人物挥手3秒，然后向前五步走，并跳跃")

# 导出 FBX
exporter = FBXExporter()
fbx_result = exporter.export_actions_to_fbx(
    actions=result['actions'],
    output_path="animation.fbx"
)
```

## 📁 项目结构

```
motion-agent/
├── backend/
│   ├── agent/                 # AI Agent 模块
│   │   ├── animation_agent.py # LangGraph Agent
│   │   └── action_parser.py   # 规则解析器
│   ├── actions/               # 动作定义
│   │   └── game_action_command_set.json
│   ├── blender/               # Blender 集成
│   │   ├── animation_schema.py # 数据格式定义
│   │   ├── converter.py       # 格式转换器
│   │   ├── blender_script.py  # Blender 脚本
│   │   └── fbx_exporter.py    # FBX 导出器
│   ├── utils/                 # 工具函数
│   └── app.py                 # FastAPI 应用
├── frontend/
│   └── src/
│       └── components/
│           └── animation-preview.tsx # 3D 预览组件
└── exports/                   # 导出文件目录
```

## 🔧 环境要求

### 后端依赖
- Python 3.12+
- LangChain & LangGraph
- FastAPI
- Pydantic
- Loguru

### 前端依赖
- React 19+
- Next.js 15+
- @react-three/fiber
- @react-three/drei
- Three.js

### 可选依赖
- Blender 3.0+ (用于 FBX 导出)
- OpenAI API Key (用于 LLM 功能)

## 🎯 特性亮点

1. **双模式解析**: 支持 LLM 和规则两种解析方式
2. **完整验证**: 自动验证生成的动作命令格式
3. **错误处理**: 完善的错误处理和日志记录
4. **API 文档**: 自动生成的 Swagger 文档
5. **扩展性**: 易于添加新的动作类型和解析规则
6. **3D 预览**: 实时预览生成的动画效果
7. **格式兼容**: 生成标准的 FBX 文件，兼容主流 3D 软件

## 📊 测试结果

- ✅ 自然语言解析功能正常
- ✅ 游戏动作命令生成正确
- ✅ Blender 格式转换成功
- ✅ JSON 数据格式验证通过
- ✅ API 接口功能完整
- ✅ 前端 3D 预览组件工作正常
- ⚠️ FBX 导出需要 Blender 环境

## 🔮 未来扩展

1. **更多动作类型**: 扩展游戏动作命令集
2. **高级动画**: 支持更复杂的动画序列
3. **多角色**: 支持多角色协同动画
4. **实时预览**: 在线实时预览动画效果
5. **云端处理**: 云端 Blender 渲染服务
6. **AI 优化**: 使用 AI 优化动画质量

## 📝 总结

Motion Agent 成功实现了从自然语言到 FBX 动画文件的完整转换流程。系统具有良好的架构设计、完整的功能实现和友好的用户接口。通过 LangChain 和 LangGraph 的强大能力，结合 Blender 的专业 3D 处理功能，为用户提供了一个高效、易用的动画生成解决方案。
