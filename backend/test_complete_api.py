"""
测试完整的 Motion Agent API，包括 FBX 导出功能
"""

import requests
import json
import time


def test_complete_workflow():
    """测试完整工作流程"""
    base_url = "http://localhost:8000"
    
    print("=== Motion Agent 完整工作流程测试 ===")
    
    # 1. 检查 API 状态
    print("\n1. 检查 API 状态")
    try:
        response = requests.get(f"{base_url}/health")
        health_data = response.json()
        print(f"API 状态: {health_data}")
        
        if not health_data.get("components", {}).get("action_parser"):
            print("❌ 动作解析器不可用")
            return False
            
    except Exception as e:
        print(f"❌ API 连接失败: {e}")
        return False
    
    # 2. 检查 Blender 状态
    print("\n2. 检查 Blender 状态")
    try:
        response = requests.get(f"{base_url}/blender-info")
        blender_info = response.json()
        print(f"Blender 信息: {blender_info}")
        
        blender_available = blender_info.get("available", False)
        if blender_available:
            print("✅ Blender 可用")
        else:
            print("⚠️ Blender 不可用，将跳过 FBX 导出测试")
            
    except Exception as e:
        print(f"❌ 获取 Blender 信息失败: {e}")
        blender_available = False
    
    # 3. 解析自然语言
    print("\n3. 解析自然语言")
    test_input = "人物挥手3秒，然后向前五步走，并跳跃"
    
    try:
        payload = {
            "text": test_input,
            "use_llm": False  # 使用规则解析器
        }
        
        response = requests.post(f"{base_url}/parse", json=payload)
        parse_data = response.json()
        
        print(f"输入: {test_input}")
        print(f"解析成功: {parse_data.get('success')}")
        print(f"生成动作数量: {len(parse_data.get('actions', []))}")
        print(f"解析方法: {parse_data.get('method')}")
        
        if not parse_data.get("success"):
            print(f"❌ 解析失败: {parse_data.get('error')}")
            return False
        
        actions = parse_data.get("actions", [])
        if not actions:
            print("❌ 没有生成任何动作")
            return False
            
        print("✅ 自然语言解析成功")
        
        # 显示生成的动作
        print("\n生成的动作:")
        for i, action in enumerate(actions, 1):
            print(f"  {i}. {action.get('action')} - {action.get('description')}")
        
    except Exception as e:
        print(f"❌ 解析请求失败: {e}")
        return False
    
    # 4. 验证动作
    print("\n4. 验证动作")
    try:
        payload = {"actions": actions}
        response = requests.post(f"{base_url}/validate", json=payload)
        validation_data = response.json()
        
        print(f"验证结果: {'有效' if validation_data.get('valid') else '无效'}")
        
        if validation_data.get("errors"):
            print(f"错误: {validation_data['errors']}")
        if validation_data.get("warnings"):
            print(f"警告: {validation_data['warnings']}")
        
        if not validation_data.get("valid"):
            print("❌ 动作验证失败")
            return False
            
        print("✅ 动作验证成功")
        
    except Exception as e:
        print(f"❌ 验证请求失败: {e}")
        return False
    
    # 5. 导出 FBX（如果 Blender 可用）
    if blender_available:
        print("\n5. 导出 FBX")
        try:
            payload = {
                "actions": actions,
                "project_name": "TestWorkflow",
                "character_name": "TestCharacter",
                "output_filename": "test_workflow.fbx"
            }
            
            print("正在导出 FBX，请稍候...")
            response = requests.post(f"{base_url}/export-fbx", json=payload)
            
            if response.status_code == 200:
                export_data = response.json()
                print(f"FBX 导出成功: {export_data.get('success')}")
                
                if export_data.get("success"):
                    print(f"输出路径: {export_data.get('output_path')}")
                    print(f"文件大小: {export_data.get('file_size')} 字节")
                    print(f"下载链接: {base_url}{export_data.get('download_url')}")
                    print("✅ FBX 导出成功")
                    
                    # 6. 测试下载
                    print("\n6. 测试文件下载")
                    download_url = export_data.get('download_url')
                    if download_url:
                        try:
                            download_response = requests.get(f"{base_url}{download_url}")
                            if download_response.status_code == 200:
                                print(f"✅ 文件下载成功，大小: {len(download_response.content)} 字节")
                            else:
                                print(f"❌ 文件下载失败: {download_response.status_code}")
                        except Exception as e:
                            print(f"❌ 下载测试失败: {e}")
                else:
                    print(f"❌ FBX 导出失败: {export_data.get('error')}")
                    return False
            else:
                print(f"❌ FBX 导出请求失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ FBX 导出异常: {e}")
            return False
    else:
        print("\n5. 跳过 FBX 导出（Blender 不可用）")
    
    print("\n🎉 完整工作流程测试完成！")
    return True


def test_api_endpoints():
    """测试各个 API 端点"""
    base_url = "http://localhost:8000"
    
    print("\n=== API 端点测试 ===")
    
    endpoints = [
        ("/", "根端点"),
        ("/health", "健康检查"),
        ("/actions", "支持的动作列表"),
        ("/blender-info", "Blender 信息")
    ]
    
    for endpoint, description in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}")
            print(f"✅ {description} ({endpoint}): {response.status_code}")
            
            if endpoint == "/":
                data = response.json()
                print(f"   版本: {data.get('version')}")
                print(f"   LLM 可用: {data.get('llm_available')}")
            elif endpoint == "/actions":
                data = response.json()
                print(f"   动作数量: {data.get('count')}")
                
        except Exception as e:
            print(f"❌ {description} ({endpoint}): {e}")


def main():
    """主函数"""
    print("Motion Agent 完整 API 测试")
    print("=" * 50)
    
    # 测试 API 端点
    test_api_endpoints()
    
    # 测试完整工作流程
    success = test_complete_workflow()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 所有测试通过！")
        print("\n功能总结:")
        print("✅ 自然语言解析")
        print("✅ 动作验证")
        print("✅ JSON 到 Blender 格式转换")
        print("✅ API 接口")
        
        print("\n使用说明:")
        print("1. 发送自然语言到 /parse 端点")
        print("2. 验证生成的动作使用 /validate 端点")
        print("3. 导出 FBX 使用 /export-fbx 端点（需要 Blender）")
        print("4. 下载文件使用 /download/{filename} 端点")
        
    else:
        print("❌ 部分测试失败")
    
    print("\nAPI 文档: http://localhost:8000/docs")


if __name__ == "__main__":
    main()
