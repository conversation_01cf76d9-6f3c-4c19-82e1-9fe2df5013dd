"""
测试统一的动作命令格式
验证 Agent 和 Blender 使用相同的数据结构
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.schemas.action_command import (
    ActionCommand, ActionType, ActionKey, ActionSequence,
    create_action_command, validate_action_parameters
)
from backend.agent.action_parser import ActionParser
from backend.blender.converter import GameActionToBlenderConverter


def test_unified_action_command():
    """测试统一的动作命令格式"""
    print("=== 测试统一的动作命令格式 ===")
    
    # 创建示例动作命令
    actions = [
        create_action_command(
            action_type=ActionType.EMOTION,
            parameters={"emotion": "happy", "intensity": 0.7},
            sequence_number=1,
            description_en="Express happy emotion by waving",
            description_zh="挥手表达快乐情绪"
        ),
        create_action_command(
            action_type=ActionType.IDLE,
            parameters={"mood": "alert", "duration": 3.0},
            sequence_number=2,
            description_en="Maintain waving gesture for 3 seconds",
            description_zh="保持挥手姿势3秒"
        ),
        create_action_command(
            action_type=ActionType.MOVE,
            parameters={"direction": "forward", "speed": 0.5, "duration": 5.0},
            sequence_number=3,
            description_en="Move forward for five steps",
            description_zh="向前走五步"
        ),
        create_action_command(
            action_type=ActionType.JUMP,
            parameters={"height": 1.0, "distance": 1.0, "style": "normal"},
            sequence_number=4,
            description_en="Perform a jump action",
            description_zh="执行跳跃动作"
        )
    ]
    
    print(f"创建了 {len(actions)} 个动作命令")
    
    # 验证格式
    for i, action in enumerate(actions, 1):
        print(f"\n动作 {i}:")
        print(f"  类型: {action.action_type}")
        print(f"  键: {action.action_key}")
        print(f"  参数: {action.parameters}")
        print(f"  序号: {action.sequence_number}")
        print(f"  英文描述: {action.description_en}")
        print(f"  中文描述: {action.description_zh}")
        
        # 验证参数
        is_valid = validate_action_parameters(action.action_type, action.parameters)
        print(f"  参数有效: {is_valid}")
    
    # 创建动作序列
    sequence = ActionSequence(
        sequence_name="TestSequence",
        actions=actions
    )
    
    sequence.calculate_total_duration()
    print(f"\n动作序列:")
    print(f"  名称: {sequence.sequence_name}")
    print(f"  总时长: {sequence.total_duration} 秒")
    print(f"  帧率: {sequence.frame_rate} FPS")
    
    # 保存为 JSON
    actions_dict = [action.model_dump() for action in actions]
    with open("unified_actions.json", 'w', encoding='utf-8') as f:
        json.dump(actions_dict, f, indent=2, ensure_ascii=False)
    
    print(f"\n✅ 统一格式测试完成，数据已保存到 unified_actions.json")
    return actions_dict


def test_parser_with_unified_format():
    """测试解析器使用统一格式"""
    print("\n=== 测试解析器使用统一格式 ===")
    
    parser = ActionParser()
    example_input = "人物挥手3秒，然后向前五步走，并跳跃"
    
    print(f"输入: {example_input}")
    
    try:
        actions = parser.parse_simple_command(example_input)
        print(f"解析成功，生成了 {len(actions)} 个动作")
        
        # 验证格式
        for i, action in enumerate(actions, 1):
            print(f"\n动作 {i}:")
            print(f"  action_type: {action.get('action_type')}")
            print(f"  action_key: {action.get('action_key')}")
            print(f"  parameters: {action.get('parameters')}")
            print(f"  sequence_number: {action.get('sequence_number')}")
            print(f"  description_en: {action.get('description_en')}")
            print(f"  description_zh: {action.get('description_zh')}")
            
            # 检查必需字段
            required_fields = ["action_type", "action_key", "parameters", "sequence_number", "description_en"]
            missing_fields = [field for field in required_fields if field not in action]
            if missing_fields:
                print(f"  ❌ 缺少字段: {missing_fields}")
            else:
                print(f"  ✅ 格式正确")
        
        # 保存解析结果
        with open("parser_unified_actions.json", 'w', encoding='utf-8') as f:
            json.dump(actions, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ 解析器统一格式测试完成")
        return actions
        
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        return []


def test_blender_converter_with_unified_format():
    """测试 Blender 转换器使用统一格式"""
    print("\n=== 测试 Blender 转换器使用统一格式 ===")
    
    # 使用解析器生成的动作
    parser = ActionParser()
    actions = parser.parse_simple_command("人物挥手3秒，然后向前五步走，并跳跃")
    
    print(f"输入动作数量: {len(actions)}")
    
    try:
        converter = GameActionToBlenderConverter()
        blender_project = converter.convert_actions_to_blender(actions, "UnifiedFormatTest")
        
        print(f"转换成功:")
        print(f"  项目名称: {blender_project.project_name}")
        print(f"  动画片段数量: {len(blender_project.animation_clips)}")
        
        if blender_project.animation_clips:
            clip = blender_project.animation_clips[0]
            print(f"  动画名称: {clip.name}")
            print(f"  帧范围: {clip.start_frame} - {clip.end_frame}")
            print(f"  动画通道数量: {len(clip.channels)}")
        
        # 保存 Blender 项目数据
        with open("unified_blender_project.json", 'w', encoding='utf-8') as f:
            json.dump(blender_project.model_dump(), f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ Blender 转换器统一格式测试完成")
        return True
        
    except Exception as e:
        print(f"❌ Blender 转换失败: {e}")
        return False


def test_format_consistency():
    """测试格式一致性"""
    print("\n=== 测试格式一致性 ===")
    
    # 1. 创建标准格式
    standard_action = create_action_command(
        action_type=ActionType.MOVE,
        parameters={"direction": "forward", "speed": 0.5, "duration": 2.0},
        sequence_number=1,
        description_en="Move forward",
        description_zh="向前移动"
    )
    
    # 2. 解析器生成格式
    parser = ActionParser()
    parser_actions = parser.parse_simple_command("向前移动")
    
    if parser_actions:
        parser_action = parser_actions[0]
        
        print("标准格式字段:")
        standard_fields = set(standard_action.model_dump().keys())
        print(f"  {sorted(standard_fields)}")
        
        print("\n解析器格式字段:")
        parser_fields = set(parser_action.keys())
        print(f"  {sorted(parser_fields)}")
        
        # 检查一致性
        missing_in_parser = standard_fields - parser_fields
        extra_in_parser = parser_fields - standard_fields
        
        if missing_in_parser:
            print(f"\n❌ 解析器缺少字段: {missing_in_parser}")
        if extra_in_parser:
            print(f"\n⚠️ 解析器额外字段: {extra_in_parser}")
        
        if not missing_in_parser and not extra_in_parser:
            print(f"\n✅ 格式完全一致!")
        else:
            print(f"\n⚠️ 格式存在差异")
    
    return True


def main():
    """主函数"""
    print("Motion Agent 统一格式测试")
    print("=" * 50)
    
    # 测试统一格式
    test_unified_action_command()
    
    # 测试解析器
    test_parser_with_unified_format()
    
    # 测试 Blender 转换器
    test_blender_converter_with_unified_format()
    
    # 测试格式一致性
    test_format_consistency()
    
    print("\n" + "=" * 50)
    print("🎉 统一格式测试完成!")
    
    print("\n📋 生成的文件:")
    files = [
        "unified_actions.json",
        "parser_unified_actions.json", 
        "unified_blender_project.json"
    ]
    
    for filename in files:
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            print(f"  ✅ {filename} - {size} 字节")
        else:
            print(f"  ❌ {filename} - 未生成")
    
    print("\n📖 格式说明:")
    print("- action_type: 动作类型 (英文)")
    print("- action_key: 动作键 (大写英文)")
    print("- parameters: 参数字典 (英文 key)")
    print("- sequence_number: 序列号 (数字)")
    print("- description_en: 英文描述")
    print("- description_zh: 中文描述")


if __name__ == "__main__":
    main()
