"""
测试示例输入："人物挥手3秒，然后向前五步走，并跳跃"
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.agent.action_parser import ActionParser


def test_example_input():
    """测试示例输入"""
    print("=== 测试示例输入 ===")
    
    # 示例输入
    example_input = "人物挥手3秒，然后向前五步走，并跳跃"
    print(f"输入: {example_input}")
    
    # 创建解析器
    parser = ActionParser()
    
    # 解析
    try:
        actions = parser.parse_simple_command(example_input)
        print(f"\n解析成功！生成了 {len(actions)} 个动作:")
        print(json.dumps(actions, indent=2, ensure_ascii=False))
        
        # 验证
        validation = parser.validate_command_sequence(actions)
        print(f"\n验证结果: {'有效' if validation['valid'] else '无效'}")
        
        if validation['errors']:
            print(f"错误: {validation['errors']}")
        if validation['warnings']:
            print(f"警告: {validation['warnings']}")
            
        return actions
        
    except Exception as e:
        print(f"解析失败: {e}")
        return None


def analyze_parsing_logic():
    """分析解析逻辑"""
    print("\n=== 解析逻辑分析 ===")
    
    parser = ActionParser()
    example_input = "人物挥手3秒，然后向前五步走，并跳跃"
    
    # 分析各个组件
    print(f"输入文本: {example_input}")
    
    # 时间提取
    time_info = parser.extract_time_info(example_input)
    print(f"时间信息: {time_info}")
    
    # 移动提取
    movements = parser.extract_movement_info(example_input)
    print(f"移动信息: {movements}")
    
    # 情绪提取
    emotions = parser.extract_emotions(example_input)
    print(f"情绪信息: {emotions}")
    
    # 动作提取
    actions = parser.extract_actions(example_input)
    print(f"动作信息: {actions}")


def create_expected_output():
    """创建期望的输出格式"""
    print("\n=== 期望的输出格式 ===")
    
    expected_actions = [
        {
            "action": "emotion",
            "key": "EMOTION",
            "params": {
                "emotion": "happy",
                "intensity": 0.7
            },
            "sequence": 1,
            "description": "挥手表达情绪"
        },
        {
            "action": "idle",
            "key": "IDLE",
            "params": {
                "mood": "alert",
                "duration": 3.0
            },
            "sequence": 2,
            "description": "挥手动作持续3秒"
        },
        {
            "action": "move",
            "key": "MOVE",
            "params": {
                "direction": "forward",
                "speed": 0.5,
                "duration": 5.0
            },
            "sequence": 3,
            "description": "向前走五步"
        },
        {
            "action": "jump",
            "key": "JUMP",
            "params": {
                "height": 1.0,
                "distance": 1.0,
                "style": "normal"
            },
            "sequence": 4,
            "description": "跳跃动作"
        }
    ]
    
    print("期望输出:")
    print(json.dumps(expected_actions, indent=2, ensure_ascii=False))
    
    return expected_actions


def improve_parser():
    """改进解析器以更好地处理示例输入"""
    print("\n=== 改进解析器 ===")
    
    parser = ActionParser()
    example_input = "人物挥手3秒，然后向前五步走，并跳跃"
    
    # 手动构建更完整的解析结果
    commands = []
    sequence = 1
    
    # 1. 挥手情绪
    commands.append(parser.create_action_command(
        "emotion",
        {"emotion": "happy", "intensity": 0.7},
        sequence,
        "挥手表达情绪"
    ))
    sequence += 1
    
    # 2. 持续3秒的待机状态
    commands.append(parser.create_action_command(
        "idle",
        {"mood": "alert", "duration": 3.0},
        sequence,
        "挥手动作持续3秒"
    ))
    sequence += 1
    
    # 3. 向前移动
    commands.append(parser.create_action_command(
        "move",
        {"direction": "forward", "speed": 0.5, "duration": 5.0},
        sequence,
        "向前走五步"
    ))
    sequence += 1
    
    # 4. 跳跃
    commands.append(parser.create_action_command(
        "jump",
        {"height": 1.0, "distance": 1.0, "style": "normal"},
        sequence,
        "跳跃动作"
    ))
    
    print("改进后的解析结果:")
    print(json.dumps(commands, indent=2, ensure_ascii=False))
    
    # 验证
    validation = parser.validate_command_sequence(commands)
    print(f"\n验证结果: {'有效' if validation['valid'] else '无效'}")
    
    return commands


if __name__ == "__main__":
    # 运行所有测试
    analyze_parsing_logic()
    test_example_input()
    create_expected_output()
    improve_parser()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("\n总结:")
    print("1. 规则解析器能够识别基本的情绪、移动和动作")
    print("2. 需要改进时间序列和复杂动作的解析")
    print("3. JSON 输出格式符合要求")
    print("4. 可以通过 LLM 获得更准确的解析结果")
