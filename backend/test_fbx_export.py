"""
测试 FBX 导出功能
"""

import sys
import os
import json
import asyncio

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.blender.converter import GameActionToBlenderConverter
from backend.blender.fbx_exporter import FBXExporter
from backend.agent.action_parser import ActionParser


def test_converter():
    """测试转换器"""
    print("=== 测试游戏动作到 Blender 格式转换器 ===")
    
    # 示例动作
    actions = [
        {
            "action": "emotion",
            "key": "EMOTION",
            "params": {
                "emotion": "happy",
                "intensity": 0.7
            },
            "sequence": 1,
            "description": "挥手表达情绪"
        },
        {
            "action": "idle",
            "key": "IDLE",
            "params": {
                "mood": "alert",
                "duration": 3.0
            },
            "sequence": 2,
            "description": "挥手动作持续3秒"
        },
        {
            "action": "move",
            "key": "MOVE",
            "params": {
                "direction": "forward",
                "speed": 0.5,
                "duration": 5.0
            },
            "sequence": 3,
            "description": "向前走五步"
        },
        {
            "action": "jump",
            "key": "JUMP",
            "params": {
                "height": 1.0,
                "distance": 1.0,
                "style": "normal"
            },
            "sequence": 4,
            "description": "跳跃动作"
        }
    ]
    
    # 创建转换器
    converter = GameActionToBlenderConverter()
    
    try:
        # 转换
        blender_project = converter.convert_actions_to_blender(actions, "TestAnimation")
        
        print(f"转换成功！")
        print(f"项目名称: {blender_project.project_name}")
        print(f"动画片段数量: {len(blender_project.animation_clips)}")
        
        if blender_project.animation_clips:
            clip = blender_project.animation_clips[0]
            print(f"动画名称: {clip.name}")
            print(f"帧范围: {clip.start_frame} - {clip.end_frame}")
            print(f"动画通道数量: {len(clip.channels)}")
            
            # 显示前几个通道
            for i, channel in enumerate(clip.channels[:3]):
                print(f"  通道 {i+1}: {channel.target_object}.{channel.target_property}")
                print(f"    类型: {channel.animation_type}")
                print(f"    关键帧数量: {len(channel.keyframes)}")
        
        # 保存为 JSON 文件用于调试
        output_file = "test_blender_project.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(blender_project.dict(), f, indent=2, ensure_ascii=False)
        
        print(f"\nBlender 项目数据已保存到: {output_file}")
        
        return blender_project
        
    except Exception as e:
        print(f"转换失败: {e}")
        return None


def test_fbx_exporter():
    """测试 FBX 导出器"""
    print("\n=== 测试 FBX 导出器 ===")
    
    # 创建导出器
    exporter = FBXExporter()
    
    # 检查 Blender 可用性
    blender_info = exporter.get_blender_info()
    print(f"Blender 可用性: {blender_info}")
    
    if not blender_info["available"]:
        print("Blender 不可用，跳过 FBX 导出测试")
        print("请确保 Blender 已安装并在 PATH 中，或设置正确的 blender_executable 路径")
        return False
    
    # 示例动作
    actions = [
        {
            "action": "move",
            "key": "MOVE",
            "params": {
                "direction": "forward",
                "speed": 0.5,
                "duration": 2.0
            },
            "sequence": 1,
            "description": "向前移动"
        },
        {
            "action": "jump",
            "key": "JUMP",
            "params": {
                "height": 1.5,
                "distance": 1.0,
                "style": "normal"
            },
            "sequence": 2,
            "description": "跳跃"
        }
    ]
    
    # 验证动作
    validation = exporter.validate_actions_for_export(actions)
    print(f"动作验证结果: {validation}")
    
    if not validation["valid"]:
        print("动作验证失败，无法导出")
        return False
    
    # 导出 FBX
    output_path = "test_animation.fbx"
    
    try:
        print(f"开始导出 FBX 到: {output_path}")
        result = exporter.export_actions_to_fbx(
            actions=actions,
            output_path=output_path,
            project_name="TestAnimation",
            character_name="TestCharacter"
        )
        
        print(f"导出结果: {result}")
        
        if result["success"]:
            print("FBX 导出成功！")
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"文件大小: {file_size} 字节")
            return True
        else:
            print(f"FBX 导出失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"FBX 导出异常: {e}")
        return False


def test_full_pipeline():
    """测试完整流程"""
    print("\n=== 测试完整流程 ===")
    
    # 1. 使用解析器生成动作
    parser = ActionParser()
    example_input = "人物挥手3秒，然后向前五步走，并跳跃"
    
    print(f"输入: {example_input}")
    
    # 解析动作
    actions = parser.parse_simple_command(example_input)
    print(f"解析出 {len(actions)} 个动作")
    
    # 2. 转换为 Blender 格式
    converter = GameActionToBlenderConverter()
    blender_project = converter.convert_actions_to_blender(actions, "FullPipelineTest")
    
    print(f"转换为 Blender 项目: {blender_project.project_name}")
    
    # 3. 尝试导出 FBX（如果 Blender 可用）
    exporter = FBXExporter()
    blender_info = exporter.get_blender_info()
    
    if blender_info["available"]:
        print("Blender 可用，尝试导出 FBX...")
        
        output_path = "full_pipeline_test.fbx"
        result = exporter.export_actions_to_fbx(
            actions=actions,
            output_path=output_path,
            project_name="FullPipelineTest"
        )
        
        if result["success"]:
            print(f"完整流程成功！FBX 文件: {output_path}")
        else:
            print(f"FBX 导出失败: {result.get('error')}")
    else:
        print("Blender 不可用，跳过 FBX 导出")
        print("但 Blender 项目数据生成成功")
    
    return True


def main():
    """主函数"""
    print("Motion Agent FBX 导出功能测试")
    print("=" * 50)
    
    # 测试转换器
    blender_project = test_converter()
    
    # 测试 FBX 导出器
    test_fbx_exporter()
    
    # 测试完整流程
    test_full_pipeline()
    
    print("\n" + "=" * 50)
    print("测试完成")
    
    print("\n使用说明:")
    print("1. 如果 Blender 不可用，请安装 Blender 并确保在 PATH 中")
    print("2. 或者在创建 FBXExporter 时指定 blender_executable 路径")
    print("3. 生成的 JSON 文件可以手动导入 Blender 进行测试")


if __name__ == "__main__":
    main()
