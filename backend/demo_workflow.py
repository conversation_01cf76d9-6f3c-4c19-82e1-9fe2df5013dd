"""
演示完整的 Motion Agent 工作流程
从自然语言到 FBX 文件的完整流程
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.agent.action_parser import ActionParser
from backend.blender.converter import GameActionToBlenderConverter
from backend.blender.fbx_exporter import FBXExporter
from backend.utils.file import save_json_to_output, get_output_file_path, ensure_output_directory


def demo_complete_workflow():
    """演示完整工作流程"""
    print("🎬 Motion Agent 完整工作流程演示")
    print("=" * 60)
    
    # 步骤 1: 自然语言输入
    print("\n📝 步骤 1: 自然语言输入")
    natural_language = "人物挥手3秒，然后向前五步走，并跳跃"
    print(f"输入: {natural_language}")
    
    # 步骤 2: 解析为游戏动作命令
    print("\n🔍 步骤 2: 解析为游戏动作命令")
    parser = ActionParser()
    
    try:
        actions = parser.parse_simple_command(natural_language)
        print(f"✅ 解析成功，生成了 {len(actions)} 个动作:")
        
        for i, action in enumerate(actions, 1):
            print(f"  {i}. {action['action_type']} ({action['action_key']})")
            print(f"     描述: {action['description_zh']}")
            print(f"     参数: {action['parameters']}")
        
        # 保存动作到输出目录
        actions_file = save_json_to_output(actions, "demo_actions", add_timestamp=True)
        print(f"\n💾 动作数据已保存到: {actions_file}")
        
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        return False
    
    # 步骤 3: 转换为 Blender 格式
    print("\n🎨 步骤 3: 转换为 Blender 动画格式")
    converter = GameActionToBlenderConverter()
    
    try:
        blender_project = converter.convert_actions_to_blender(
            actions, 
            project_name="DemoAnimation"
        )
        
        print(f"✅ 转换成功:")
        print(f"  项目名称: {blender_project.project_name}")
        print(f"  动画片段: {len(blender_project.animation_clips)}")
        
        if blender_project.animation_clips:
            clip = blender_project.animation_clips[0]
            print(f"  动画名称: {clip.name}")
            print(f"  帧范围: {clip.start_frame} - {clip.end_frame}")
            print(f"  动画通道: {len(clip.channels)}")
            print(f"  帧率: {clip.frame_rate} FPS")
        
        # 保存 Blender 项目数据到输出目录
        blender_file = save_json_to_output(
            blender_project.model_dump(),
            "demo_blender_project",
            add_timestamp=True
        )
        print(f"\n💾 Blender 项目数据已保存到: {blender_file}")
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        return False
    
    # 步骤 4: 检查 Blender 可用性
    print("\n🔧 步骤 4: 检查 Blender 环境")
    exporter = FBXExporter()
    blender_info = exporter.get_blender_info()
    
    print(f"Blender 可用: {blender_info['available']}")
    if blender_info['available']:
        print(f"版本信息: {blender_info.get('version', 'Unknown')}")
    else:
        print(f"错误信息: {blender_info.get('error', 'Unknown')}")
    
    # 步骤 5: 导出 FBX（如果 Blender 可用）
    if blender_info['available']:
        print("\n📦 步骤 5: 导出 FBX 文件")
        
        try:
            # 生成输出路径到 output 目录
            output_path = get_output_file_path("demo_animation.fbx", add_timestamp=True)

            print("正在导出 FBX 文件，请稍候...")
            result = exporter.export_actions_to_fbx(
                actions=actions,
                output_path=output_path,
                project_name="DemoAnimation",
                character_name="DemoCharacter"
            )
            
            if result["success"]:
                print(f"✅ FBX 导出成功!")
                print(f"  输出路径: {result['output_path']}")
                print(f"  文件大小: {result.get('file_size', 0)} 字节")
                print(f"  动作数量: {result['actions_count']}")
                
                # 验证文件存在
                if os.path.exists(output_path):
                    file_size = os.path.getsize(output_path)
                    print(f"  实际文件大小: {file_size} 字节")
                    print(f"📁 FBX 文件已生成: {os.path.abspath(output_path)}")
                else:
                    print("⚠️ FBX 文件未找到")
                    
            else:
                print(f"❌ FBX 导出失败: {result.get('error')}")
                return False
                
        except Exception as e:
            print(f"❌ FBX 导出异常: {e}")
            return False
    else:
        print("\n⚠️ 步骤 5: 跳过 FBX 导出（Blender 不可用）")
        print("💡 提示: 安装 Blender 并确保在 PATH 中可访问以启用 FBX 导出功能")
    
    # 总结
    print("\n🎉 工作流程演示完成!")
    print("\n📋 生成的文件 (保存在 output 目录):")

    # 确保输出目录存在并列出文件
    output_dir = ensure_output_directory()
    print(f"📁 输出目录: {output_dir}")

    # 列出 JSON 文件
    from backend.utils.file import list_output_files
    json_files = list_output_files(file_extension=".json")
    fbx_files = list_output_files(file_extension=".fbx")

    print("\n📄 JSON 文件:")
    for file_info in json_files:
        if "demo" in file_info['name']:
            print(f"  ✅ {file_info['name']} - {file_info['size']} 字节")
            print(f"     路径: {file_info['path']}")

    if blender_info['available'] and fbx_files:
        print("\n🎬 FBX 文件:")
        for file_info in fbx_files:
            if "demo" in file_info['name']:
                print(f"  ✅ {file_info['name']} - {file_info['size']} 字节")
                print(f"     路径: {file_info['path']}")

    if not json_files and not fbx_files:
        print("  ❌ 未找到生成的文件")
    
    return True


def show_usage_instructions():
    """显示使用说明"""
    print("\n📖 使用说明")
    print("-" * 40)
    print("1. 自然语言输入示例:")
    print("   - '人物挥手3秒，然后向前五步走，并跳跃'")
    print("   - '角色向左移动10步，然后蹲下'")
    print("   - '开心地跳跃，接着向右走3步'")
    
    print("\n2. 生成的文件用途:")
    print("   - demo_actions.json: 可用于 API 调用或进一步处理")
    print("   - demo_blender_project.json: 可手动导入 Blender")
    print("   - demo_animation.fbx: 可直接在 3D 软件中使用")
    
    print("\n3. Blender 安装:")
    print("   - 下载: https://www.blender.org/download/")
    print("   - 确保 'blender' 命令在 PATH 中可用")
    print("   - 或在代码中指定 Blender 可执行文件路径")
    
    print("\n4. API 使用:")
    print("   - 启动服务器: python -m uvicorn backend.app:app --reload")
    print("   - API 文档: http://localhost:8000/docs")
    print("   - 解析端点: POST /parse")
    print("   - 导出端点: POST /export-fbx")


def main():
    """主函数"""
    print("Motion Agent - 自然语言到 FBX 动画转换演示")
    print("=" * 60)
    
    # 运行演示
    success = demo_complete_workflow()
    
    # 显示使用说明
    show_usage_instructions()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 演示成功完成!")
    else:
        print("❌ 演示过程中出现错误")
    
    print("\n💡 提示: 查看生成的 JSON 文件了解数据格式")
    print("🔗 项目地址: https://github.com/your-repo/motion-agent")


if __name__ == "__main__":
    main()
