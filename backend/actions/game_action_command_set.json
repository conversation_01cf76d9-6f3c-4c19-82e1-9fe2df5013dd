[{"name": "move", "key": "MOVE", "desc": "基础移动 / Basic Movement", "params": {"direction": "forward | backward | left | right | up | down", "speed": "float (0.0 - 1.0)", "duration": "float (seconds)"}}, {"name": "jump", "key": "JUMP", "desc": "跳跃 / Jump", "params": {"height": "float", "distance": "float", "style": "normal | high | long"}}, {"name": "attack", "key": "ATTACK", "desc": "攻击动作 / Attack", "params": {"weapon": "sword | gun | magic | fist | bow | spear", "style": "slash | thrust | shoot | cast | rapid | charged", "strength": "float (0.0 - 1.0)", "target": "string"}}, {"name": "defend", "key": "DEFEND", "desc": "防御动作 / Defend", "params": {"type": "block | dodge | parry | shield", "direction": "front | left | right | back"}}, {"name": "idle", "key": "IDLE", "desc": "待机状态 / Idle State", "params": {"mood": "relaxed | alert | injured | thinking | bored", "duration": "float (seconds)"}}, {"name": "state_transition", "key": "STATE", "desc": "角色状态转换 / State Transition", "params": {"from": "idle | combat | walk | run | sneak | prone | swimming | flying", "to": "idle | combat | walk | run | sneak | prone | swimming | flying"}}, {"name": "ui_animation", "key": "UI", "desc": "UI 动画效果 / UI Animation", "params": {"type": "fade_in | fade_out | bounce | scale | slide | flash", "element": "inventory | map | health_bar | dialogue_box", "duration": "float (seconds)"}}, {"name": "emotion", "key": "EMOTION", "desc": "情绪表达 / Emotion", "params": {"emotion": "happy | angry | sad | surprised | scared | confused | confident", "intensity": "float (0.0 - 1.0)"}}, {"name": "interaction", "key": "INTERACT", "desc": "环境互动 / Environment Interaction", "params": {"object": "door | lever | item | npc | chest | button | computer", "action": "open | pull | press | examine | talk | use"}}, {"name": "contextual_action", "key": "CONTEXT", "desc": "情景动作 / Contextual Action", "params": {"context": "ledge | vault | crawl_space | ladder | wall | obstacle", "action": "climb | vault | crawl | slide | hang"}}, {"name": "combo", "key": "COMBO", "desc": "连击动作 / Combo", "params": {"sequence": [], "timing": []}}, {"name": "roll", "key": "ROLL", "desc": "翻滚 / Dodge Roll", "params": {"direction": "forward | backward | left | right", "distance": "float"}}, {"name": "sneak", "key": "SNEAK", "desc": "潜行 / Sneak", "params": {"speed": "float (0.0 - 0.5)", "crouch": "boolean", "mode": "slow | silent"}}, {"name": "drive", "key": "DRIVE", "desc": "驾驶 / Drive", "params": {"vehicle": "car | bike | tank | boat | aircraft", "speed": "float", "mode": "normal | boost | slow", "action": "accelerate | brake | turn"}}, {"name": "team_action", "key": "TEAM", "desc": "组队动作 / Team Coordination", "params": {"role": "leader | follower | support", "action": "signal | assist | sync_move | revive"}}, {"name": "cinematic", "key": "CINEMA", "desc": "演出过场 / Cinematic", "params": {"camera_motion": "pan | zoom | rotate | dolly | track", "actor_action": "pose | speak | react | walk_to | look_at", "event": "cutscene | monologue"}}, {"name": "look", "key": "LOOK", "desc": "看向某个方向或目标 / Look at a direction or target", "params": {"direction": "forward | backward | left | right | up | down", "target": "string"}}, {"name": "pick_up", "key": "PICKUP", "desc": "拾取物品 / Pick up an item", "params": {"item": "string", "from_location": "ground | table | npc"}}, {"name": "drop", "key": "DROP", "desc": "丢弃物品 / Drop an item", "params": {"item": "string"}}, {"name": "use_item", "key": "USE_ITEM", "desc": "使用物品 / Use an item from inventory", "params": {"item": "string", "target": "self | ally | enemy | environment"}}, {"name": "equip", "key": "EQUIP", "desc": "装备物品 / Equip an item", "params": {"item": "string", "slot": "main_hand | off_hand | head | chest | legs | feet"}}, {"name": "unequip", "key": "UNEQUIP", "desc": "卸下物品 / Unequip an item", "params": {"item": "string", "slot": "main_hand | off_hand | head | chest | legs | feet"}}, {"name": "heal", "key": "HEAL", "desc": "治疗动作 / Perform a healing action", "params": {"type": "potion | spell | bandage", "target": "self | ally"}}, {"name": "crouch", "key": "CROUCH", "desc": "蹲下 / Crouch", "params": {"toggle": "boolean"}}, {"name": "open", "key": "OPEN", "desc": "打开 / Open something", "params": {"object": "door | chest | gate", "direction": "inward | outward | left | right"}}, {"name": "close", "key": "CLOSE", "desc": "关闭 / Close something", "params": {"object": "door | chest | gate"}}, {"name": "talk", "key": "TALK", "desc": "与NPC对话 / Talk to an NPC", "params": {"npc_name": "string", "topic": "string"}}]