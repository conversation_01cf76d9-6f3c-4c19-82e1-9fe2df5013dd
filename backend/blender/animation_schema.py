"""
Blender 动画数据格式定义
定义从游戏动作命令到 Blender 动画数据的转换格式
"""

from typing import List, Dict, Any, Optional, Tuple
from pydantic import BaseModel, Field
from enum import Enum


class BlenderAnimationType(str, Enum):
    """Blender 动画类型"""
    LOCATION = "location"
    ROTATION = "rotation"
    SCALE = "scale"
    BONE_ROTATION = "bone_rotation"
    BONE_LOCATION = "bone_location"
    SHAPE_KEY = "shape_key"
    CUSTOM_PROPERTY = "custom_property"


class InterpolationMode(str, Enum):
    """插值模式"""
    LINEAR = "LINEAR"
    BEZIER = "BEZIER"
    CONSTANT = "CONSTANT"
    EASE_IN = "EASE_IN"
    EASE_OUT = "EASE_OUT"
    EASE_IN_OUT = "EASE_IN_OUT"


class BlenderKeyframe(BaseModel):
    """Blender 关键帧数据"""
    frame: int = Field(..., description="帧号")
    value: List[float] = Field(..., description="关键帧值 (x, y, z 或其他)")
    interpolation: InterpolationMode = Field(InterpolationMode.LINEAR, description="插值模式")
    handle_left: Optional[Tuple[float, float]] = Field(None, description="左控制点")
    handle_right: Optional[Tuple[float, float]] = Field(None, description="右控制点")


class BlenderAnimationChannel(BaseModel):
    """Blender 动画通道"""
    target_object: str = Field(..., description="目标对象名称")
    target_property: str = Field(..., description="目标属性路径")
    animation_type: BlenderAnimationType = Field(..., description="动画类型")
    keyframes: List[BlenderKeyframe] = Field(..., description="关键帧列表")
    bone_name: Optional[str] = Field(None, description="骨骼名称（如果是骨骼动画）")


class BlenderAnimationClip(BaseModel):
    """Blender 动画片段"""
    name: str = Field(..., description="动画名称")
    start_frame: int = Field(1, description="开始帧")
    end_frame: int = Field(..., description="结束帧")
    frame_rate: float = Field(24.0, description="帧率")
    channels: List[BlenderAnimationChannel] = Field(..., description="动画通道列表")
    markers: List[Dict[str, Any]] = Field(default_factory=list, description="时间标记")


class BlenderScene(BaseModel):
    """Blender 场景数据"""
    scene_name: str = Field("MotionAgentScene", description="场景名称")
    frame_rate: float = Field(24.0, description="场景帧率")
    frame_start: int = Field(1, description="场景开始帧")
    frame_end: int = Field(250, description="场景结束帧")
    objects: List[Dict[str, Any]] = Field(default_factory=list, description="场景对象")


class BlenderExportSettings(BaseModel):
    """Blender 导出设置"""
    export_format: str = Field("FBX", description="导出格式")
    export_path: str = Field(..., description="导出路径")
    include_animations: bool = Field(True, description="包含动画")
    include_armatures: bool = Field(True, description="包含骨架")
    include_meshes: bool = Field(True, description="包含网格")
    fbx_version: str = Field("7.4.0", description="FBX 版本")
    scale_factor: float = Field(1.0, description="缩放因子")


class BlenderProject(BaseModel):
    """完整的 Blender 项目数据"""
    project_name: str = Field(..., description="项目名称")
    scene: BlenderScene = Field(..., description="场景数据")
    animation_clips: List[BlenderAnimationClip] = Field(..., description="动画片段列表")
    export_settings: BlenderExportSettings = Field(..., description="导出设置")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


# 动作到 Blender 属性的映射
ACTION_TO_BLENDER_MAPPING = {
    "move": {
        "forward": {"property": "location", "axis": "y", "multiplier": 1.0},
        "backward": {"property": "location", "axis": "y", "multiplier": -1.0},
        "left": {"property": "location", "axis": "x", "multiplier": -1.0},
        "right": {"property": "location", "axis": "x", "multiplier": 1.0},
        "up": {"property": "location", "axis": "z", "multiplier": 1.0},
        "down": {"property": "location", "axis": "z", "multiplier": -1.0},
    },
    "jump": {
        "property": "location",
        "axis": "z",
        "keyframes": [
            {"frame_offset": 0, "value": 0},
            {"frame_offset": 0.3, "value": 1.0},  # 跳跃顶点
            {"frame_offset": 0.6, "value": 0},    # 落地
        ]
    },
    "rotation": {
        "property": "rotation_euler",
        "axes": ["x", "y", "z"]
    },
    "emotion": {
        # 情绪可以通过形状键或骨骼动画表现
        "happy": {"shape_keys": {"Smile": 1.0, "EyeSquint": 0.3}},
        "angry": {"shape_keys": {"Frown": 1.0, "EyeNarrow": 0.8}},
        "sad": {"shape_keys": {"Frown": 0.7, "EyeClose": 0.4}},
        "surprised": {"shape_keys": {"EyeWide": 1.0, "MouthOpen": 0.6}},
    }
}

# 默认的角色骨骼映射
DEFAULT_CHARACTER_BONES = {
    "root": "Root",
    "spine": "Spine",
    "chest": "Chest",
    "neck": "Neck",
    "head": "Head",
    "left_arm": "LeftArm",
    "right_arm": "RightArm",
    "left_leg": "LeftLeg",
    "right_leg": "RightLeg",
    "left_hand": "LeftHand",
    "right_hand": "RightHand",
    "left_foot": "LeftFoot",
    "right_foot": "RightFoot"
}

# 帧率和时间转换
FRAME_RATE = 24.0  # 默认帧率

def seconds_to_frames(seconds: float, frame_rate: float = FRAME_RATE) -> int:
    """将秒转换为帧数"""
    return int(seconds * frame_rate)

def frames_to_seconds(frames: int, frame_rate: float = FRAME_RATE) -> float:
    """将帧数转换为秒"""
    return frames / frame_rate
