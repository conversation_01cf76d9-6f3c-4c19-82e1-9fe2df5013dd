import json
import os
from typing import Dict, Any, Optional
from datetime import datetime
from loguru import logger

def load_json_file(file_path: str):
    """加载 JSON 文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def ensure_output_directory(base_path: str = None) -> str:
    """确保输出目录存在

    Args:
        base_path: 基础输出路径，默认为 backend/output

    Returns:
        str: 输出目录的绝对路径
    """
    if base_path is None:
        # 默认使用 backend/output 目录
        current_dir = os.path.dirname(os.path.abspath(__file__))  # backend/utils
        backend_dir = os.path.dirname(current_dir)  # backend
        base_path = os.path.join(backend_dir, "output")

    output_dir = os.path.abspath(base_path)
    os.makedirs(output_dir, exist_ok=True)
    logger.info(f"输出目录已准备: {output_dir}")
    return output_dir

def save_json_to_output(data: Dict[str, Any], filename: str,
                       output_dir: str = None,
                       add_timestamp: bool = False) -> str:
    """将 JSON 数据保存到输出目录

    Args:
        data: 要保存的数据
        filename: 文件名（不包含扩展名）
        output_dir: 输出目录，默认为 backend/output
        add_timestamp: 是否在文件名中添加时间戳

    Returns:
        str: 保存的文件路径
    """
    # 确保输出目录存在
    output_path = ensure_output_directory(output_dir)

    # 处理文件名
    if add_timestamp:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{filename}_{timestamp}"

    if not filename.endswith('.json'):
        filename += '.json'

    file_path = os.path.join(output_path, filename)

    # 保存文件
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)

    logger.info(f"JSON 文件已保存: {file_path}")
    return file_path

def get_output_file_path(filename: str, output_dir: str = None,
                        add_timestamp: bool = False) -> str:
    """获取输出文件的完整路径

    Args:
        filename: 文件名
        output_dir: 输出目录，默认为 backend/output
        add_timestamp: 是否在文件名中添加时间戳

    Returns:
        str: 完整的文件路径
    """
    # 确保输出目录存在
    output_path = ensure_output_directory(output_dir)

    # 处理文件名
    if add_timestamp:
        name, ext = os.path.splitext(filename)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{name}_{timestamp}{ext}"

    return os.path.join(output_path, filename)

def list_output_files(output_dir: str = None,
                     file_extension: Optional[str] = None) -> list:
    """列出输出目录中的文件

    Args:
        output_dir: 输出目录，默认为 backend/output
        file_extension: 文件扩展名过滤器（如 ".json", ".fbx"）

    Returns:
        list: 文件列表
    """
    output_path = ensure_output_directory(output_dir)

    if not os.path.exists(output_path):
        return []

    files = []
    for file in os.listdir(output_path):
        file_path = os.path.join(output_path, file)
        if os.path.isfile(file_path):
            if file_extension is None or file.endswith(file_extension):
                files.append({
                    'name': file,
                    'path': file_path,
                    'size': os.path.getsize(file_path),
                    'modified': datetime.fromtimestamp(os.path.getmtime(file_path))
                })

    return sorted(files, key=lambda x: x['modified'], reverse=True)