"""
测试 Blender 配置功能
验证 macOS 上的 Blender 自动检测和配置
"""

import sys
import os
import platform

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.blender.blender_config import BlenderConfig
from backend.blender.fbx_exporter import FBXExporter


def test_blender_config():
    """测试 Blender 配置管理器"""
    print("=== 测试 Blender 配置管理器 ===")
    
    config = BlenderConfig()
    
    print(f"操作系统: {platform.system()}")
    print(f"配置文件: {config.config_file}")
    
    # 显示默认搜索路径
    search_paths = config._get_default_search_paths()
    print(f"\n默认搜索路径:")
    for i, path in enumerate(search_paths, 1):
        print(f"  {i}. {path}")
    
    # 自动检测 Blender
    print(f"\n正在自动检测 Blender...")
    detected_path = config.detect_blender()
    
    if detected_path:
        print(f"✅ 检测到 Blender: {detected_path}")
        
        # 获取详细信息
        blender_info = config.get_blender_info(detected_path)
        print(f"版本信息:")
        for key, value in blender_info.items():
            if key != "full_version_info":
                print(f"  {key}: {value}")
    else:
        print(f"❌ 未检测到 Blender")
        
        # 显示安装说明
        install_info = config.install_instructions()
        print(f"\n安装说明:")
        print(f"方法: {install_info['method']}")
        print(f"下载地址: {install_info['url']}")
        print(f"安装步骤:\n{install_info['instructions']}")
        print(f"典型路径: {install_info['typical_path']}")
    
    return detected_path


def test_fbx_exporter_with_config():
    """测试带配置的 FBX 导出器"""
    print("\n=== 测试 FBX 导出器配置 ===")
    
    try:
        # 创建导出器（自动检测 Blender）
        exporter = FBXExporter()
        
        print(f"FBX 导出器初始化完成")
        print(f"Blender 路径: {exporter.blender_executable}")
        
        # 获取 Blender 信息
        blender_info = exporter.get_blender_info()
        print(f"\nBlender 状态:")
        print(f"  可用: {blender_info['available']}")
        
        if blender_info['available']:
            print(f"  版本: {blender_info.get('version', 'Unknown')}")
            print(f"  路径: {blender_info.get('path', 'Unknown')}")
        else:
            print(f"  错误: {blender_info.get('error', 'Unknown')}")
        
        # 获取导出设置
        export_settings = exporter.get_export_settings()
        print(f"\n导出设置:")
        for key, value in export_settings.items():
            print(f"  {key}: {value}")
        
        # 获取安装指南
        install_guide = exporter.get_install_instructions()
        print(f"\n安装指南:")
        print(f"  推荐路径: {exporter.config.get_recommended_blender_path()}")
        print(f"  安装方法: {install_guide['method']}")
        
        return blender_info['available']
        
    except Exception as e:
        print(f"❌ FBX 导出器测试失败: {e}")
        return False


def test_manual_configuration():
    """测试手动配置 Blender 路径"""
    print("\n=== 测试手动配置 ===")
    
    # 常见的 macOS Blender 路径
    test_paths = [
        "/Applications/Blender.app/Contents/MacOS/Blender",
        "/usr/local/bin/blender",
        "blender"
    ]
    
    config = BlenderConfig()
    
    for path in test_paths:
        print(f"\n测试路径: {path}")
        
        if config._test_blender_path(path):
            print(f"✅ 路径有效")
            
            # 尝试设置
            if config.set_blender_path(path):
                print(f"✅ 配置成功")
                
                # 验证配置
                info = config.get_blender_info(path)
                print(f"版本: {info.get('version', 'Unknown')}")
                break
            else:
                print(f"❌ 配置失败")
        else:
            print(f"❌ 路径无效或不可用")
    
    return True


def test_api_integration():
    """测试 API 集成"""
    print("\n=== 测试 API 集成 ===")
    
    try:
        import requests
        import json
        
        base_url = "http://localhost:8000"
        
        # 测试 Blender 信息端点
        print("测试 /blender-info 端点...")
        try:
            response = requests.get(f"{base_url}/blender-info", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Blender 信息获取成功")
                print(f"  可用: {data.get('available', False)}")
                if data.get('available'):
                    print(f"  版本: {data.get('version', 'Unknown')}")
            else:
                print(f"❌ 请求失败: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"⚠️ API 服务器未运行: {e}")
        
        # 测试安装指南端点
        print("\n测试 /blender-install-guide 端点...")
        try:
            response = requests.get(f"{base_url}/blender-install-guide", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 安装指南获取成功")
                print(f"  推荐路径: {data.get('recommended_path', 'Unknown')}")
                print(f"  当前状态: {data.get('current_status', {}).get('available', False)}")
            else:
                print(f"❌ 请求失败: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"⚠️ API 服务器未运行: {e}")
        
        # 测试自动检测端点
        print("\n测试 /detect-blender 端点...")
        try:
            response = requests.post(f"{base_url}/detect-blender", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 自动检测完成")
                print(f"  检测成功: {data.get('detection_success', False)}")
                print(f"  消息: {data.get('message', 'Unknown')}")
            else:
                print(f"❌ 请求失败: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"⚠️ API 服务器未运行: {e}")
        
        return True
        
    except ImportError:
        print("⚠️ requests 库未安装，跳过 API 测试")
        return False


def main():
    """主函数"""
    print("Motion Agent - Blender 配置测试")
    print("=" * 50)
    
    # 显示系统信息
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"Python 版本: {platform.python_version()}")
    
    # 测试配置管理器
    detected_path = test_blender_config()
    
    # 测试 FBX 导出器
    exporter_available = test_fbx_exporter_with_config()
    
    # 测试手动配置
    test_manual_configuration()
    
    # 测试 API 集成
    test_api_integration()
    
    print("\n" + "=" * 50)
    print("🎉 Blender 配置测试完成!")
    
    # 总结
    print(f"\n📋 测试结果:")
    print(f"  自动检测: {'✅ 成功' if detected_path else '❌ 失败'}")
    print(f"  FBX 导出器: {'✅ 可用' if exporter_available else '❌ 不可用'}")
    
    if not detected_path:
        print(f"\n💡 建议:")
        print(f"1. 从 https://www.blender.org/download/ 下载 Blender")
        print(f"2. 安装到 /Applications/Blender.app")
        print(f"3. 或使用 Homebrew: brew install --cask blender")
        print(f"4. 使用 API 端点 /configure-blender 手动设置路径")
    
    print(f"\n🔧 配置文件位置: blender_config.json")
    print(f"📚 API 文档: http://localhost:8000/docs")


if __name__ == "__main__":
    main()
