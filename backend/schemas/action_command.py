"""
统一的动作命令格式定义
Agent 和 Blender 公用的 Action Command 数据结构
"""

from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field
from enum import Enum


class ActionType(str, Enum):
    """动作类型枚举"""
    MOVE = "move"
    JUMP = "jump"
    ATTACK = "attack"
    DEFEND = "defend"
    EMOTION = "emotion"
    IDLE = "idle"
    CROUCH = "crouch"
    SNEAK = "sneak"
    ROLL = "roll"
    HEAL = "heal"
    PICK_UP = "pick_up"
    DROP = "drop"
    EQUIP = "equip"
    UNEQUIP = "unequip"
    OPEN = "open"
    CLOSE = "close"
    TALK = "talk"
    CAST = "cast"
    BLOCK = "block"
    DODGE = "dodge"
    CLIMB = "climb"
    SWIM = "swim"
    FLY = "fly"
    DANCE = "dance"
    SIT = "sit"
    SLEEP = "sleep"
    WAKE_UP = "wake_up"


class ActionKey(str, Enum):
    """动作键枚举"""
    MOVE = "MOVE"
    JUMP = "JUMP"
    ATTACK = "ATTACK"
    DEFEND = "DEFEND"
    EMOTION = "EMOTION"
    IDLE = "IDLE"
    CROUCH = "CROUCH"
    SNEAK = "SNEAK"
    ROLL = "ROLL"
    HEAL = "HEAL"
    PICK_UP = "PICK_UP"
    DROP = "DROP"
    EQUIP = "EQUIP"
    UNEQUIP = "UNEQUIP"
    OPEN = "OPEN"
    CLOSE = "CLOSE"
    TALK = "TALK"
    CAST = "CAST"
    BLOCK = "BLOCK"
    DODGE = "DODGE"
    CLIMB = "CLIMB"
    SWIM = "SWIM"
    FLY = "FLY"
    DANCE = "DANCE"
    SIT = "SIT"
    SLEEP = "SLEEP"
    WAKE_UP = "WAKE_UP"


class ActionParameters(BaseModel):
    """动作参数基类"""
    duration: Optional[float] = Field(None, description="Duration in seconds")
    intensity: Optional[float] = Field(None, description="Intensity (0.0-1.0)")
    speed: Optional[float] = Field(None, description="Speed multiplier")


class MoveParameters(ActionParameters):
    """移动动作参数"""
    direction: str = Field(..., description="Movement direction: forward, backward, left, right, up, down")
    distance: Optional[float] = Field(None, description="Distance to move")
    speed: float = Field(0.5, description="Movement speed (0.0-2.0)")
    duration: float = Field(1.0, description="Movement duration in seconds")


class JumpParameters(ActionParameters):
    """跳跃动作参数"""
    height: float = Field(1.0, description="Jump height")
    distance: float = Field(1.0, description="Jump distance")
    style: str = Field("normal", description="Jump style: normal, high, long")


class AttackParameters(ActionParameters):
    """攻击动作参数"""
    weapon: str = Field("fist", description="Weapon type")
    target: Optional[str] = Field(None, description="Target object")
    style: str = Field("slash", description="Attack style")
    strength: float = Field(0.8, description="Attack strength (0.0-1.0)")


class EmotionParameters(ActionParameters):
    """情绪动作参数"""
    emotion: str = Field(..., description="Emotion type: happy, sad, angry, surprised, etc.")
    intensity: float = Field(0.7, description="Emotion intensity (0.0-1.0)")
    duration: float = Field(2.0, description="Emotion duration in seconds")


class IdleParameters(ActionParameters):
    """待机动作参数"""
    mood: str = Field("relaxed", description="Idle mood: relaxed, alert, bored, etc.")
    duration: float = Field(1.0, description="Idle duration in seconds")


class ActionCommand(BaseModel):
    """统一的动作命令格式"""
    action_type: ActionType = Field(..., description="Action type")
    action_key: ActionKey = Field(..., description="Action key for identification")
    parameters: Dict[str, Any] = Field(..., description="Action parameters")
    sequence_number: int = Field(..., description="Sequence number in animation")
    description_en: str = Field(..., description="English description")
    description_zh: Optional[str] = Field(None, description="Chinese description")
    
    # Blender 相关字段
    start_frame: Optional[int] = Field(None, description="Start frame in Blender timeline")
    end_frame: Optional[int] = Field(None, description="End frame in Blender timeline")
    blender_channels: Optional[List[str]] = Field(None, description="Blender animation channels")
    
    # 元数据
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

    class Config:
        use_enum_values = True


class ActionSequence(BaseModel):
    """动作序列"""
    sequence_name: str = Field(..., description="Sequence name")
    actions: List[ActionCommand] = Field(..., description="List of actions")
    total_duration: Optional[float] = Field(None, description="Total sequence duration")
    frame_rate: float = Field(24.0, description="Animation frame rate")
    
    # 自动计算总时长
    def calculate_total_duration(self) -> float:
        """计算总时长"""
        total = 0.0
        for action in self.actions:
            duration = action.parameters.get("duration", 1.0)
            total += duration
        self.total_duration = total
        return total


# 动作类型到参数类的映射
ACTION_PARAMETER_MAPPING = {
    ActionType.MOVE: MoveParameters,
    ActionType.JUMP: JumpParameters,
    ActionType.ATTACK: AttackParameters,
    ActionType.EMOTION: EmotionParameters,
    ActionType.IDLE: IdleParameters,
}

# 动作类型到键的映射
ACTION_TYPE_TO_KEY = {
    ActionType.MOVE: ActionKey.MOVE,
    ActionType.JUMP: ActionKey.JUMP,
    ActionType.ATTACK: ActionKey.ATTACK,
    ActionType.DEFEND: ActionKey.DEFEND,
    ActionType.EMOTION: ActionKey.EMOTION,
    ActionType.IDLE: ActionKey.IDLE,
    ActionType.CROUCH: ActionKey.CROUCH,
    ActionType.SNEAK: ActionKey.SNEAK,
    ActionType.ROLL: ActionKey.ROLL,
    ActionType.HEAL: ActionKey.HEAL,
    ActionType.PICK_UP: ActionKey.PICK_UP,
    ActionType.DROP: ActionKey.DROP,
    ActionType.EQUIP: ActionKey.EQUIP,
    ActionType.UNEQUIP: ActionKey.UNEQUIP,
    ActionType.OPEN: ActionKey.OPEN,
    ActionType.CLOSE: ActionKey.CLOSE,
    ActionType.TALK: ActionKey.TALK,
    ActionType.CAST: ActionKey.CAST,
    ActionType.BLOCK: ActionKey.BLOCK,
    ActionType.DODGE: ActionKey.DODGE,
    ActionType.CLIMB: ActionKey.CLIMB,
    ActionType.SWIM: ActionKey.SWIM,
    ActionType.FLY: ActionKey.FLY,
    ActionType.DANCE: ActionKey.DANCE,
    ActionType.SIT: ActionKey.SIT,
    ActionType.SLEEP: ActionKey.SLEEP,
    ActionType.WAKE_UP: ActionKey.WAKE_UP,
}


def create_action_command(
    action_type: ActionType,
    parameters: Dict[str, Any],
    sequence_number: int,
    description_en: str,
    description_zh: Optional[str] = None
) -> ActionCommand:
    """创建动作命令的工厂函数
    
    Args:
        action_type: 动作类型
        parameters: 参数字典
        sequence_number: 序列号
        description_en: 英文描述
        description_zh: 中文描述
        
    Returns:
        ActionCommand: 动作命令对象
    """
    return ActionCommand(
        action_type=action_type,
        action_key=ACTION_TYPE_TO_KEY[action_type],
        parameters=parameters,
        sequence_number=sequence_number,
        description_en=description_en,
        description_zh=description_zh
    )


def validate_action_parameters(action_type: ActionType, parameters: Dict[str, Any]) -> bool:
    """验证动作参数
    
    Args:
        action_type: 动作类型
        parameters: 参数字典
        
    Returns:
        bool: 是否有效
    """
    try:
        parameter_class = ACTION_PARAMETER_MAPPING.get(action_type, ActionParameters)
        parameter_class(**parameters)
        return True
    except Exception:
        return False
