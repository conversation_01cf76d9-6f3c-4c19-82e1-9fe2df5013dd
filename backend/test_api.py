"""
测试 Motion Agent API
"""

import requests
import json


def test_api_endpoints():
    """测试 API 端点"""
    base_url = "http://localhost:8000"
    
    print("=== 测试 Motion Agent API ===")
    
    # 测试根端点
    print("\n1. 测试根端点")
    try:
        response = requests.get(f"{base_url}/")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 测试健康检查
    print("\n2. 测试健康检查")
    try:
        response = requests.get(f"{base_url}/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 测试获取支持的动作
    print("\n3. 测试获取支持的动作")
    try:
        response = requests.get(f"{base_url}/actions")
        data = response.json()
        print(f"状态码: {response.status_code}")
        print(f"动作数量: {data.get('count', 0)}")
        if data.get('actions'):
            print(f"前3个动作: {[a['name'] for a in data['actions'][:3]]}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 测试解析（规则模式）
    print("\n4. 测试解析（规则模式）")
    test_cases = [
        "人物挥手3秒，然后向前五步走，并跳跃",
        "角色向左移动10步，然后蹲下",
        "开心地跳跃，接着向右走3步"
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n4.{i} 测试用例: {test_case}")
        try:
            payload = {
                "text": test_case,
                "use_llm": False
            }
            response = requests.post(f"{base_url}/parse", json=payload)
            data = response.json()
            print(f"状态码: {response.status_code}")
            print(f"解析成功: {data.get('success', False)}")
            print(f"动作数量: {len(data.get('actions', []))}")
            print(f"解析方法: {data.get('method', 'unknown')}")
            
            if data.get('actions'):
                print("动作列表:")
                for action in data['actions']:
                    print(f"  - {action.get('action')} ({action.get('key')}): {action.get('description')}")
            
        except Exception as e:
            print(f"错误: {e}")
    
    # 测试验证
    print("\n5. 测试动作验证")
    try:
        test_actions = [
            {
                "action": "move",
                "key": "MOVE",
                "params": {
                    "direction": "forward",
                    "speed": 0.5,
                    "duration": 3.0
                },
                "sequence": 1,
                "description": "向前移动"
            },
            {
                "action": "jump",
                "key": "JUMP",
                "params": {
                    "height": 1.0,
                    "distance": 1.0,
                    "style": "normal"
                },
                "sequence": 2,
                "description": "跳跃"
            }
        ]
        
        payload = {"actions": test_actions}
        response = requests.post(f"{base_url}/validate", json=payload)
        data = response.json()
        print(f"状态码: {response.status_code}")
        print(f"验证结果: {'有效' if data.get('valid') else '无效'}")
        print(f"命令数量: {data.get('command_count', 0)}")
        
        if data.get('errors'):
            print(f"错误: {data['errors']}")
        if data.get('warnings'):
            print(f"警告: {data['warnings']}")
            
    except Exception as e:
        print(f"错误: {e}")


if __name__ == "__main__":
    test_api_endpoints()
