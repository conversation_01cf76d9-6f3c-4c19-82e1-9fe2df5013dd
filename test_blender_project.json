{"project_name": "TestAnimation", "scene": {"scene_name": "TestAnimation_Scene", "frame_rate": 24.0, "frame_start": 1, "frame_end": 265, "objects": []}, "animation_clips": [{"name": "TestAnimation_Animation", "start_frame": 1, "end_frame": 265, "frame_rate": 24.0, "channels": [{"target_object": "Character", "target_property": "key_blocks[\"Smile\"].value", "animation_type": "shape_key", "keyframes": [{"frame": 1, "value": [0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 6, "value": [0.7], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 44, "value": [0.7], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 49, "value": [0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}], "bone_name": null}, {"target_object": "Character", "target_property": "key_blocks[\"EyeSquint\"].value", "animation_type": "shape_key", "keyframes": [{"frame": 1, "value": [0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 6, "value": [0.21], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 44, "value": [0.21], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 49, "value": [0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}], "bone_name": null}, {"target_object": "Character", "target_property": "location", "animation_type": "location", "keyframes": [{"frame": 49, "value": [0.0, 0.0, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 55, "value": [0.0, 0.0, 0.009320390859672264], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 61, "value": [0.0, 0.0, 0.006754631805511506], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 67, "value": [0.0, 0.0, -0.0044252044329485245], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 73, "value": [0.0, 0.0, -0.009961646088358407], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 79, "value": [0.0, 0.0, -0.0027941549819892587], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 85, "value": [0.0, 0.0, 0.007936678638491531], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 91, "value": [0.0, 0.0, 0.008545989080882804], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 97, "value": [0.0, 0.0, -0.001743267812229814], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 103, "value": [0.0, 0.0, -0.009809362300664915], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 109, "value": [0.0, 0.0, -0.00536572918000435], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 115, "value": [0.0, 0.0, 0.005920735147072245], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}], "bone_name": null}, {"target_object": "Character", "target_property": "location", "animation_type": "location", "keyframes": [{"frame": 121, "value": [0.0, 0.0, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 241, "value": [0.0, 12.5, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}], "bone_name": null}, {"target_object": "Character", "target_property": "location", "animation_type": "location", "keyframes": [{"frame": 241, "value": [0.0, 0.0, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 250, "value": [0.0, 0.0, 1.0], "interpolation": "EASE_OUT", "handle_left": null, "handle_right": null}, {"frame": 265, "value": [0.0, 0.0, 0.0], "interpolation": "EASE_IN", "handle_left": null, "handle_right": null}], "bone_name": null}, {"target_object": "Character", "target_property": "location", "animation_type": "location", "keyframes": [{"frame": 241, "value": [0.0, 0.0, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 265, "value": [0.0, 1.0, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}], "bone_name": null}], "markers": []}], "export_settings": {"export_format": "FBX", "export_path": "/tmp/TestAnimation.fbx", "include_animations": true, "include_armatures": true, "include_meshes": true, "fbx_version": "7.4.0", "scale_factor": 1.0}, "metadata": {"source": "MotionAgent", "actions_count": 4, "total_frames": 265}}