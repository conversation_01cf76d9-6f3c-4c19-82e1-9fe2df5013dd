{"project_name": "DemoAnimation", "scene": {"scene_name": "DemoAnimation_Scene", "frame_rate": 24.0, "frame_start": 1, "frame_end": 73, "objects": []}, "animation_clips": [{"name": "DemoAnimation_Animation", "start_frame": 1, "end_frame": 73, "frame_rate": 24.0, "channels": [{"target_object": "Character", "target_property": "key_blocks[\"Smile\"].value", "animation_type": "shape_key", "keyframes": [{"frame": 1, "value": [0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 6, "value": [0.7], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 44, "value": [0.7], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 49, "value": [0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}], "bone_name": null}, {"target_object": "Character", "target_property": "key_blocks[\"EyeSquint\"].value", "animation_type": "shape_key", "keyframes": [{"frame": 1, "value": [0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 6, "value": [0.21], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 44, "value": [0.21], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 49, "value": [0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}], "bone_name": null}, {"target_object": "Character", "target_property": "location", "animation_type": "location", "keyframes": [{"frame": 49, "value": [0.0, 0.0, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 58, "value": [0.0, 0.0, 1.0], "interpolation": "EASE_OUT", "handle_left": null, "handle_right": null}, {"frame": 73, "value": [0.0, 0.0, 0.0], "interpolation": "EASE_IN", "handle_left": null, "handle_right": null}], "bone_name": null}, {"target_object": "Character", "target_property": "location", "animation_type": "location", "keyframes": [{"frame": 49, "value": [0.0, 0.0, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 73, "value": [0.0, 1.0, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}], "bone_name": null}], "markers": []}], "export_settings": {"export_format": "FBX", "export_path": "/tmp/DemoAnimation.fbx", "include_animations": true, "include_armatures": true, "include_meshes": true, "fbx_version": "7.4.0", "scale_factor": 1.0}, "metadata": {"source": "MotionAgent", "actions_count": 2, "total_frames": 73}}